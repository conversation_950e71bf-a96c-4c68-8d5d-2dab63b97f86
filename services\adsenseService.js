class AdSenseService {
  constructor() {
    // Default AdSense ad units - these will be configurable via settings
    this.defaultAdUnits = [
      {
        id: 'ad-unit-1',
        script: `<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7010023287510094"
     crossorigin="anonymous"></script>
<ins class="adsbygoogle"
     style="display:block; text-align:center;"
     data-ad-layout="in-article"
     data-ad-format="fluid"
     data-ad-client="ca-pub-7010023287510094"
     data-ad-slot="3690800868"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>`,
        name: 'In-Article Ad Unit 1',
        enabled: true
      },
      {
        id: 'ad-unit-2',
        script: `<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7010023287510094"
     crossorigin="anonymous"></script>
<ins class="adsbygoogle"
     style="display:block; text-align:center;"
     data-ad-layout="in-article"
     data-ad-format="fluid"
     data-ad-client="ca-pub-7010023287510094"
     data-ad-slot="4291748547"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>`,
        name: 'In-Article Ad Unit 2',
        enabled: true
      },
      {
        id: 'ad-unit-3',
        script: `<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7010023287510094"
     crossorigin="anonymous"></script>
<ins class="adsbygoogle"
     style="display:block; text-align:center;"
     data-ad-layout="in-article"
     data-ad-format="fluid"
     data-ad-client="ca-pub-7010023287510094"
     data-ad-slot="2047183101"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>`,
        name: 'In-Article Ad Unit 3',
        enabled: true
      },
      {
        id: 'ad-unit-4',
        script: `<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7010023287510094"
     crossorigin="anonymous"></script>
<ins class="adsbygoogle"
     style="display:block; text-align:center;"
     data-ad-layout="in-article"
     data-ad-format="fluid"
     data-ad-client="ca-pub-7010023287510094"
     data-ad-slot="5356450371"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>`,
        name: 'In-Article Ad Unit 4',
        enabled: true
      }
    ];

    // Default settings
    this.defaultSettings = {
      autoInsertAds: true,
      adsPerArticle: 3,
      minParagraphsBetweenAds: 1, // Reduced from 2 to 1 for better insertion
      minWordsPerParagraph: 10,
      skipFirstParagraphs: 1,
      skipLastParagraphs: 1
    };

    // Storage directory for user settings
    this.storageDir = require('path').join(__dirname, '../data/adsense');
    this.ensureStorageDirectory();
  }

  /**
   * Ensure storage directory exists
   */
  ensureStorageDirectory() {
    const fs = require('fs');
    if (!fs.existsSync(this.storageDir)) {
      fs.mkdirSync(this.storageDir, { recursive: true });
    }
  }

  /**
   * Get user-specific settings file path
   * @param {string} userId - User ID (email or Google ID)
   * @returns {string} - File path for user settings
   */
  getUserSettingsPath(userId) {
    const crypto = require('crypto');
    const hashedUserId = crypto.createHash('md5').update(userId).digest('hex');
    return require('path').join(this.storageDir, `${hashedUserId}.json`);
  }

  /**
   * Load user settings from file
   * @param {string} userId - User ID
   * @returns {Object} - User settings or default settings
   */
  loadUserSettings(userId) {
    if (!userId) {
      return { ...this.defaultSettings };
    }

    try {
      const fs = require('fs');
      const settingsPath = this.getUserSettingsPath(userId);

      if (fs.existsSync(settingsPath)) {
        const settingsData = fs.readFileSync(settingsPath, 'utf8');
        const userSettings = JSON.parse(settingsData);

        // Merge with defaults to ensure all properties exist
        return { ...this.defaultSettings, ...userSettings };
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    }

    return { ...this.defaultSettings };
  }

  /**
   * Save user settings to file
   * @param {string} userId - User ID
   * @param {Object} settings - Settings to save
   */
  saveUserSettings(userId, settings) {
    if (!userId) {
      throw new Error('User ID is required to save settings');
    }

    try {
      const fs = require('fs');
      const settingsPath = this.getUserSettingsPath(userId);

      // Merge with defaults and validate
      const validatedSettings = { ...this.defaultSettings, ...settings };

      fs.writeFileSync(settingsPath, JSON.stringify(validatedSettings, null, 2));

      console.log('AdSense settings saved for user:', {
        userId: userId.substring(0, 10) + '...',
        settingsPath: settingsPath
      });
    } catch (error) {
      console.error('Error saving user settings:', error);
      throw error;
    }
  }

  /**
   * Insert AdSense ads into article content
   * @param {string} content - HTML content of the article
   * @param {Object} options - Configuration options (can include userId)
   * @returns {Object} - Result with processed content and ad insertion info
   */
  insertAdsIntoContent(content, options = {}) {
    try {
      if (!content || typeof content !== 'string') {
        throw new Error('Content is required and must be a string');
      }

      // Load user-specific settings if userId provided
      const userSettings = options.userId ? this.loadUserSettings(options.userId) : this.defaultSettings;



      // Merge user settings with options (options take precedence)
      const config = { ...userSettings, ...options };

      if (!config.autoInsertAds) {
        return {
          success: true,
          content: content,
          adsInserted: 0,
          message: 'Ad insertion is disabled'
        };
      }

      console.log('Starting AdSense ad insertion:', {
        contentLength: content.length,
        adsPerArticle: config.adsPerArticle,
        autoInsertAds: config.autoInsertAds,
        userId: options.userId ? options.userId.substring(0, 10) + '...' : 'none',
        config: config
      });

      // Parse content into paragraphs
      const paragraphs = this.parseContentIntoParagraphs(content, config);



      if (paragraphs.length < 3) {
        console.log('Content too short for ad insertion:', {
          paragraphCount: paragraphs.length,
          minimumRequired: 3
        });
        return {
          success: true,
          content: content,
          adsInserted: 0,
          message: 'Content too short for ad insertion (minimum 3 paragraphs required)'
        };
      }

      // Determine ad insertion positions
      const adPositions = this.calculateAdPositions(paragraphs, config);
      
      if (adPositions.length === 0) {
        return {
          success: true,
          content: content,
          adsInserted: 0,
          message: 'No suitable positions found for ad insertion'
        };
      }

      // Get enabled ad units
      const enabledAdUnits = this.getEnabledAdUnits();
      
      if (enabledAdUnits.length === 0) {
        return {
          success: true,
          content: content,
          adsInserted: 0,
          message: 'No enabled ad units available'
        };
      }

      // Insert ads at calculated positions
      const processedContent = this.insertAdsAtPositions(paragraphs, adPositions, enabledAdUnits);

      console.log('AdSense ad insertion completed:', {
        adsInserted: adPositions.length,
        totalParagraphs: paragraphs.length,
        adPositions: adPositions
      });

      return {
        success: true,
        content: processedContent,
        adsInserted: adPositions.length,
        adPositions: adPositions,
        totalParagraphs: paragraphs.length,
        message: `Successfully inserted ${adPositions.length} ads`
      };

    } catch (error) {
      console.error('AdSense insertion error:', error);
      return {
        success: false,
        content: content,
        adsInserted: 0,
        error: error.message
      };
    }
  }

  /**
   * Parse HTML content into paragraph blocks
   * @param {string} content - HTML content
   * @param {Object} config - Configuration object with settings
   * @returns {Array} - Array of paragraph objects
   */
  parseContentIntoParagraphs(content, config) {
    // Ensure config is valid
    if (!config) {
      config = this.defaultSettings || {
        minWordsPerParagraph: 10,
        skipFirstParagraphs: 1,
        skipLastParagraphs: 1
      };
    }

    // Match complete paragraph and block elements
    const blockElementRegex = /<(p|div|h[1-6]|blockquote|li|section|article)[^>]*>.*?<\/\1>/gi;
    const matches = content.match(blockElementRegex) || [];

    const paragraphs = [];

    for (let i = 0; i < matches.length; i++) {
      const element = matches[i];

      // Extract text content from the element
      const cleanText = element.replace(/<[^>]*>/g, '').trim();
      const wordCount = cleanText.split(/\s+/).filter(word => word.length > 0).length;

      // Only include elements with sufficient content
      if (wordCount >= config.minWordsPerParagraph) {
        paragraphs.push({
          index: i,
          content: element,
          wordCount: wordCount,
          cleanText: cleanText
        });
      }
    }

    console.log('Parsed paragraphs:', {
      totalMatches: matches.length,
      validParagraphs: paragraphs.length,
      minWordsRequired: config.minWordsPerParagraph
    });

    return paragraphs;
  }

  /**
   * Calculate optimal positions for ad insertion
   * @param {Array} paragraphs - Array of paragraph objects
   * @param {Object} config - Configuration settings
   * @returns {Array} - Array of insertion positions
   */
  calculateAdPositions(paragraphs, config) {
    const positions = [];
    const totalParagraphs = paragraphs.length;
    const availableParagraphs = totalParagraphs - config.skipFirstParagraphs - config.skipLastParagraphs;



    if (availableParagraphs <= 0) {
      return positions;
    }

    // Calculate how many ads we can actually insert
    const maxPossibleAds = Math.floor(availableParagraphs / (config.minParagraphsBetweenAds + 1));
    const adsToInsert = Math.min(config.adsPerArticle, maxPossibleAds);

    if (adsToInsert <= 0) {
      return positions;
    }

    // Distribute ads evenly throughout the content
    const spacing = Math.floor(availableParagraphs / (adsToInsert + 1));
    
    for (let i = 1; i <= adsToInsert; i++) {
      const position = config.skipFirstParagraphs + (spacing * i);
      if (position < totalParagraphs - config.skipLastParagraphs) {
        positions.push(position);
      }
    }
    
    return positions;
  }

  /**
   * Insert ads at specified positions
   * @param {Array} paragraphs - Array of paragraph objects
   * @param {Array} positions - Positions to insert ads
   * @param {Array} adUnits - Available ad units
   * @returns {string} - Content with ads inserted
   */
  insertAdsAtPositions(paragraphs, positions, adUnits) {
    let result = '';
    let adIndex = 0;
    
    for (let i = 0; i < paragraphs.length; i++) {
      // Add the paragraph content
      result += paragraphs[i].content;
      
      // Check if we should insert an ad after this paragraph
      if (positions.includes(i) && adIndex < adUnits.length) {
        const adUnit = adUnits[adIndex % adUnits.length];
        result += '\n\n' + this.wrapAdInContainer(adUnit.script) + '\n\n';
        adIndex++;
      }
    }
    
    return result;
  }

  /**
   * Wrap ad script in a container for better styling
   * @param {string} adScript - AdSense ad script
   * @returns {string} - Wrapped ad script
   */
  wrapAdInContainer(adScript) {
    return `<div class="adsense-container" style="margin: 20px 0; text-align: center; clear: both;">
  ${adScript}
</div>`;
  }

  /**
   * Get enabled ad units
   * @returns {Array} - Array of enabled ad units
   */
  getEnabledAdUnits() {
    return this.defaultAdUnits.filter(unit => unit.enabled);
  }

  /**
   * Update ad units configuration
   * @param {Array} adUnits - New ad units configuration
   */
  updateAdUnits(adUnits) {
    if (Array.isArray(adUnits)) {
      this.defaultAdUnits = adUnits;
    }
  }

  /**
   * Update settings
   * @param {Object} newSettings - New settings
   */
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
  }

  /**
   * Get settings for a specific user
   * @param {string} userId - User ID (optional)
   * @returns {Object} - User settings or default settings
   */
  getSettings(userId = null) {
    return userId ? this.loadUserSettings(userId) : { ...this.defaultSettings };
  }

  /**
   * Update settings for a specific user
   * @param {string} userId - User ID
   * @param {Object} newSettings - Settings to update
   * @returns {Object} - Updated settings
   */
  updateSettings(userId, newSettings) {
    if (!userId) {
      throw new Error('User ID is required to update settings');
    }

    // Load current settings
    const currentSettings = this.loadUserSettings(userId);

    // Merge with new settings
    const updatedSettings = { ...currentSettings, ...newSettings };

    // Save updated settings
    this.saveUserSettings(userId, updatedSettings);

    return updatedSettings;
  }

  /**
   * Get current ad units
   * @returns {Array} - Current ad units
   */
  getAdUnits() {
    return [...this.defaultAdUnits];
  }
}

module.exports = new AdSenseService();
