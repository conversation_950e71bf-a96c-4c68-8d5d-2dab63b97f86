const request = require('supertest');
const app = require('../server');

describe('Article Rewriter App', () => {
  describe('Health Check', () => {
    test('GET /health should return 200', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);
      
      expect(response.body).toHaveProperty('status', 'OK');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('environment');
    });
  });

  describe('Main Page', () => {
    test('GET / should return 200', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);
      
      expect(response.text).toContain('Article Rewriter');
    });
  });

  describe('API Configuration', () => {
    test('GET /api/config should return configuration', async () => {
      const response = await request(app)
        .get('/api/config')
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('rateLimitWindowMs');
      expect(response.body.data).toHaveProperty('rateLimitMaxRequests');
      expect(response.body.data).toHaveProperty('scrapingTimeout');
      expect(response.body.data).toHaveProperty('maxContentLength');
    });
  });

  describe('URL Validation', () => {
    test('POST /api/articles/validate-url with valid URL', async () => {
      const response = await request(app)
        .post('/api/articles/validate-url')
        .send({ url: 'https://example.com' })
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
    });

    test('POST /api/articles/validate-url with invalid URL', async () => {
      const response = await request(app)
        .post('/api/articles/validate-url')
        .send({ url: 'not-a-url' })
        .expect(400);
      
      expect(response.body).toHaveProperty('success', false);
    });

    test('POST /api/articles/validate-url without URL', async () => {
      const response = await request(app)
        .post('/api/articles/validate-url')
        .send({})
        .expect(400);
      
      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Content Scraping', () => {
    test('POST /api/articles/scrape without URL should fail', async () => {
      const response = await request(app)
        .post('/api/articles/scrape')
        .send({})
        .expect(400);
      
      expect(response.body).toHaveProperty('success', false);
    });

    test('POST /api/articles/scrape with invalid URL should fail', async () => {
      const response = await request(app)
        .post('/api/articles/scrape')
        .send({ url: 'not-a-url' })
        .expect(400);
      
      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Content Rewriting', () => {
    test('POST /api/articles/rewrite without content should fail', async () => {
      const response = await request(app)
        .post('/api/articles/rewrite')
        .send({})
        .expect(400);
      
      expect(response.body).toHaveProperty('success', false);
    });

    test('POST /api/articles/rewrite with too long content should fail', async () => {
      const longContent = 'a'.repeat(60000);
      const response = await request(app)
        .post('/api/articles/rewrite')
        .send({ content: longContent })
        .expect(400);
      
      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Authentication', () => {
    test('GET /auth/status should return authentication status', async () => {
      const response = await request(app)
        .get('/auth/status')
        .expect(200);
      
      expect(response.body).toHaveProperty('authenticated');
    });

    test('GET /api/articles/blogs without auth should fail', async () => {
      const response = await request(app)
        .get('/api/articles/blogs')
        .expect(401);
      
      expect(response.body).toHaveProperty('success', false);
    });

    test('POST /api/articles/publish without auth should fail', async () => {
      const response = await request(app)
        .post('/api/articles/publish')
        .send({
          blogId: 'test',
          title: 'Test',
          content: 'Test content'
        })
        .expect(401);
      
      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Rate Limiting', () => {
    test('Should handle multiple requests within limits', async () => {
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          request(app)
            .get('/api/config')
            .expect(200)
        );
      }
      
      await Promise.all(promises);
    });
  });

  describe('Error Handling', () => {
    test('404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/non-existent-route')
        .expect(404);
      
      expect(response.text).toContain('Page Not Found');
    });

    test('API 404 should return JSON', async () => {
      const response = await request(app)
        .get('/api/non-existent')
        .expect(404);
      
      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Security', () => {
    test('Should sanitize malicious input', async () => {
      const maliciousInput = {
        url: 'https://example.com',
        content: '<script>alert("xss")</script>Test content'
      };
      
      const response = await request(app)
        .post('/api/articles/validate-url')
        .send(maliciousInput)
        .expect(200);
      
      // The malicious script should be removed by sanitization
      expect(response.body).toHaveProperty('success', true);
    });

    test('Should reject invalid API key format', async () => {
      const response = await request(app)
        .post('/api/config/gemini-key')
        .send({ apiKey: 'invalid-key' })
        .expect(400);
      
      expect(response.body).toHaveProperty('success', false);
    });
  });
});
