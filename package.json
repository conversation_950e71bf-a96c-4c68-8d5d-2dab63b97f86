{"name": "article-rewriter-app", "version": "1.0.0", "description": "Node.js web application for article processing, rewriting, and publishing to Blogger", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["article", "rewriter", "blogger", "gemini", "ai", "seo"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.11.0", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "ejs": "^3.1.10", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "googleapis": "^154.1.0", "helmet": "^8.1.0", "jest": "^30.0.5", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemon": "^3.1.10", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "puppeteer": "^24.15.0", "supertest": "^7.1.4"}}