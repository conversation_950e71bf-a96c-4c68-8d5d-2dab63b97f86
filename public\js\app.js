// Article Rewriter App - Main JavaScript

class ArticleRewriter {
    constructor() {
        this.currentData = {
            original: null,
            rewritten: null,
            blogs: []
        };
        this.init();
    }

    init() {
        this.loadUserBlogs();
        this.setupEventListeners();
        this.checkAuthStatus();
    }

    setupEventListeners() {
        // URL validation on input
        document.getElementById('articleUrl').addEventListener('blur', () => {
            const url = document.getElementById('articleUrl').value;
            if (url) {
                this.validateUrl();
            }
        });

        // Form submission prevention
        document.getElementById('articleForm').addEventListener('submit', (e) => {
            e.preventDefault();
        });

        // Button event listeners
        document.getElementById('validateUrlBtn').addEventListener('click', () => {
            this.validateUrl();
        });

        document.getElementById('scrapeBtn').addEventListener('click', () => {
            this.scrapeArticle();
        });

        document.getElementById('rewriteBtn').addEventListener('click', () => {
            this.rewriteContent();
        });

        const publishBtn = document.getElementById('publishBtn');
        if (publishBtn) {
            publishBtn.addEventListener('click', () => {
                this.showPublishModal();
            });
        }

        const settingsBtn = document.getElementById('settingsBtn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSettings();
            });
        }

        // Initialize image upload functionality
        this.initializeImageUpload();

        // Initialize CTA configuration toggle
        this.initializeCtaConfiguration();
    }

    initializeCtaConfiguration() {
        // Add event listener for CTA checkbox toggle
        document.addEventListener('change', (e) => {
            if (e.target && e.target.id === 'insertCtaButton') {
                const ctaConfig = document.getElementById('ctaConfiguration');
                if (ctaConfig) {
                    ctaConfig.style.display = e.target.checked ? 'block' : 'none';
                }
            }
        });
    }

    async checkAuthStatus() {
        try {
            const response = await fetch('/auth/status');
            const data = await response.json();
            
            if (data.authenticated) {
                this.updateUIForAuthenticatedUser(data.user);
            }
        } catch (error) {
            console.error('Error checking auth status:', error);
        }
    }

    updateUIForAuthenticatedUser(user) {
        // Update UI elements for authenticated user
        const publishBtn = document.getElementById('publishBtn');
        if (publishBtn) {
            publishBtn.style.display = 'inline-block';
        }
    }

    async validateUrl() {
        const urlInput = document.getElementById('articleUrl');
        const url = urlInput.value.trim();
        
        if (!url) return;

        try {
            const response = await fetch('/api/articles/validate-url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ url })
            });

            const data = await response.json();
            
            if (data.valid) {
                this.showAlert('URL is valid and accessible', 'success');
                urlInput.classList.remove('is-invalid');
                urlInput.classList.add('is-valid');
            } else {
                this.showAlert(`URL validation failed: ${data.error}`, 'warning');
                urlInput.classList.remove('is-valid');
                urlInput.classList.add('is-invalid');
            }
        } catch (error) {
            console.error('URL validation error:', error);
            this.showAlert('Error validating URL', 'danger');
        }
    }

    async scrapeArticle() {
        const url = document.getElementById('articleUrl').value.trim();
        const articleType = document.getElementById('articleType').value;

        if (!url) {
            this.showAlert('Please enter a valid URL', 'warning');
            return;
        }

        this.showProgress('Scraping article content...', 25);

        try {
            const response = await fetch('/api/articles/scrape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ url, articleType })
            });

            const data = await response.json();

            if (data.success) {
                this.currentData.original = data.data;
                this.displayOriginalContent(data.data);
                this.enableButton('rewriteBtn');
                this.showAlert('Article scraped successfully!', 'success');
                this.addToRecentActivity('Scraped article', url);
            } else {
                this.showAlert(`Scraping failed: ${data.error}`, 'danger');
            }
        } catch (error) {
            console.error('Scraping error:', error);
            this.showAlert('Error scraping article', 'danger');
        } finally {
            this.hideProgress();
        }
    }

    async rewriteContent() {
        if (!this.currentData.original) {
            this.showAlert('Please scrape an article first', 'warning');
            return;
        }

        const customPrompt = document.getElementById('customPrompt').value.trim();
        const articleType = document.getElementById('articleType').value;

        // Debug logging
        console.log('Rewrite request data:', {
            hasContent: !!this.currentData.original.content,
            contentLength: this.currentData.original.content?.length || 0,
            title: this.currentData.original.title,
            articleType: articleType,
            customPromptLength: customPrompt.length
        });

        // Validate content before sending
        if (!this.currentData.original.content) {
            this.showAlert('No content available to rewrite', 'warning');
            return;
        }

        if (this.currentData.original.content.length > 50000) {
            this.showAlert('Content is too long (max 50,000 characters). Please use shorter content.', 'warning');
            return;
        }

        this.showProgress('Rewriting content with AI...', 75);

        try {
            const response = await fetch('/api/articles/rewrite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: this.currentData.original.content,
                    title: this.currentData.original.title,
                    articleType: articleType,
                    customPrompt: customPrompt
                })
            });

            const data = await response.json();

            if (data.success) {
                this.currentData.rewritten = data.data;
                this.displayRewrittenContent(data.data);
                this.enableButton('publishBtn');
                this.showAlert('Content rewritten successfully!', 'success');
                this.addToRecentActivity('Rewritten content', this.currentData.rewritten.title);
            } else {
                // Enhanced error handling with detailed messages
                let errorMessage = data.error || 'Unknown error occurred';

                // Show validation details if available
                if (data.details && Array.isArray(data.details)) {
                    console.error('Validation errors:', data.details);
                    const validationMessages = data.details.map(err => err.msg).join(', ');
                    errorMessage += ` (Validation: ${validationMessages})`;
                }

                // Show technical details in development mode
                if (data.technicalError) {
                    console.error('Technical error details:', data.technicalError);
                    errorMessage += ` (Technical: ${data.technicalError})`;
                }

                // Show timestamp if available
                if (data.timestamp) {
                    console.log('Error occurred at:', data.timestamp);
                }

                this.showAlert(`Rewriting failed: ${errorMessage}`, 'danger');

                // Provide specific guidance based on error type
                if (data.error.includes('API key')) {
                    this.showAlert('Please configure your Gemini API key in Settings', 'warning');
                } else if (data.error.includes('quota') || data.error.includes('rate limit')) {
                    this.showAlert('Try again in a few minutes when the API quota resets', 'info');
                } else if (data.error.includes('safety')) {
                    this.showAlert('Try rephrasing your content or removing potentially sensitive information', 'info');
                }
            }
        } catch (error) {
            console.error('Rewriting error:', error);

            // Enhanced network error handling
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                this.showAlert('Network error: Please check your internet connection', 'danger');
            } else if (error.name === 'SyntaxError') {
                this.showAlert('Server response error: Please try again', 'danger');
            } else {
                this.showAlert(`Error rewriting content: ${error.message}`, 'danger');
            }
        } finally {
            this.hideProgress();
        }
    }

    displayOriginalContent(data) {
        const container = document.getElementById('originalContent');
        container.innerHTML = `
            <h6 class="text-primary">${data.title}</h6>
            <div class="mb-2">
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i>Scraped: ${new Date(data.scrapedAt).toLocaleString()}
                    <span class="ms-3"><i class="fas fa-file-word me-1"></i>Words: ${data.wordCount}</span>
                </small>
            </div>
            <div class="content-preview">
                ${data.content.substring(0, 500)}${data.content.length > 500 ? '...' : ''}
            </div>
            ${data.tags && data.tags.length > 0 ? `
                <div class="mt-2">
                    <small class="text-muted">Tags: </small>
                    ${data.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            ` : ''}
        `;
        
        document.getElementById('contentArea').style.display = 'block';
    }

    displayRewrittenContent(data) {
        const container = document.getElementById('rewrittenContent');
        container.innerHTML = `
            <div class="d-flex justify-content-between align-items-start mb-2">
                <h6 class="text-success mb-0">${data.title}</h6>
                <button type="button" class="btn btn-sm btn-outline-primary" id="editContentBtn">
                    <i class="fas fa-edit me-1"></i>Edit
                </button>
            </div>
            <div class="mb-2">
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i>Rewritten: ${new Date(data.rewrittenAt).toLocaleString()}
                    <span class="ms-3"><i class="fas fa-file-word me-1"></i>Words: ${data.rewrittenWordCount}</span>
                    ${data.detectedLanguage ? `<span class="ms-3"><i class="fas fa-language me-1"></i>Language: ${data.detectedLanguage}</span>` : ''}
                </small>
            </div>
            <div class="content-preview">
                ${data.content}
            </div>
            <div class="mt-2">
                <small class="text-muted">Meta Description: </small>
                <p class="small">${data.metaDescription}</p>
            </div>
            ${data.tags && data.tags.length > 0 ? `
                <div class="mt-2">
                    <small class="text-muted">Suggested Tags: </small>
                    ${data.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            ` : ''}
            <div class="mt-2">
                <small class="text-muted">Suggested Permalink: </small>
                <code>${data.permalink}</code>
            </div>
        `;

        // Add event listener for edit button
        const editBtn = document.getElementById('editContentBtn');
        if (editBtn) {
            editBtn.addEventListener('click', () => {
                this.showContentEditor(data);
            });
        }
    }

    showContentEditor(data) {
        // Populate the form with current data
        document.getElementById('editTitle').value = data.title || '';
        document.getElementById('editContent').value = data.content || '';
        document.getElementById('editMetaDescription').value = data.metaDescription || '';
        document.getElementById('editTags').value = data.tags ? data.tags.join(', ') : '';
        document.getElementById('editPermalink').value = data.permalink || '';

        // Show the modal
        this.waitForBootstrap(() => {
            const modalElement = document.getElementById('contentEditorModal');
            const modal = new bootstrap.Modal(modalElement);

            // Add proper focus management for accessibility
            modalElement.addEventListener('hide.bs.modal', () => {
                // Remove focus from any focused element inside the modal before hiding
                const focusedElement = modalElement.querySelector(':focus');
                if (focusedElement) {
                    focusedElement.blur();
                }
            });

            modal.show();

            // Add save button event listener
            const saveBtn = document.getElementById('saveContentBtn');
            const newSaveHandler = () => {
                this.saveContentChanges();
                saveBtn.removeEventListener('click', newSaveHandler);
            };
            saveBtn.addEventListener('click', newSaveHandler);
        });
    }

    saveContentChanges() {
        // Get form data
        const title = document.getElementById('editTitle').value.trim();
        const content = document.getElementById('editContent').value.trim();
        const metaDescription = document.getElementById('editMetaDescription').value.trim();
        const tagsInput = document.getElementById('editTags').value.trim();
        const permalink = document.getElementById('editPermalink').value.trim();

        // Validate required fields
        if (!title || !content) {
            this.showAlert('Title and content are required', 'warning');
            return;
        }

        // Process tags
        const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        // Update current data
        this.currentData.rewritten = {
            ...this.currentData.rewritten,
            title: title,
            content: content,
            metaDescription: metaDescription,
            tags: tags,
            permalink: permalink,
            editedAt: new Date().toISOString()
        };

        // Update the display
        this.displayRewrittenContent(this.currentData.rewritten);

        // Close the modal
        this.waitForBootstrap(() => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('contentEditorModal'));
            if (modal) {
                modal.hide();
            }
        });

        this.showAlert('Content updated successfully!', 'success');
        this.addToRecentActivity('Edited content', title);
    }

    async loadUserBlogs() {
        try {
            const response = await fetch('/api/articles/blogs');
            if (response.ok) {
                const data = await response.json();
                this.currentData.blogs = data.data || [];
            }
        } catch (error) {
            console.error('Error loading blogs:', error);
        }
    }

    showProgress(message, percentage) {
        const container = document.getElementById('progressContainer');
        const progressBar = container.querySelector('.progress-bar');
        const progressText = document.getElementById('progressText');
        
        container.style.display = 'block';
        progressBar.style.width = `${percentage}%`;
        progressText.textContent = message;
    }

    hideProgress() {
        document.getElementById('progressContainer').style.display = 'none';
    }

    enableButton(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = false;
        }
    }

    disableButton(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = true;
        }
    }

    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer');
        const alertId = 'alert-' + Date.now();
        
        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('beforeend', alertHTML);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    getAlertIcon(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    addToRecentActivity(action, details) {
        const container = document.getElementById('recentActivity');
        const timestamp = new Date().toLocaleTimeString();

        const activityHTML = `
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div>
                    <small class="fw-bold">${action}</small><br>
                    <small class="text-muted text-truncate-2">${details}</small>
                </div>
                <small class="text-muted">${timestamp}</small>
            </div>
        `;

        if (container.innerHTML.includes('No recent activity')) {
            container.innerHTML = activityHTML;
        } else {
            container.insertAdjacentHTML('afterbegin', activityHTML);
        }

        // Keep only last 5 activities
        const activities = container.children;
        while (activities.length > 5) {
            activities[activities.length - 1].remove();
        }
    }

    showPublishModal() {
        if (!this.currentData.rewritten) {
            this.showAlert('Please rewrite content first', 'warning');
            return;
        }

        const modalHTML = `
            <div class="modal fade" id="publishModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-upload me-2"></i>Publish to Blogger
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="publishForm">
                                <div class="mb-3">
                                    <label class="form-label">Select Blog</label>
                                    <div id="blogSelection">
                                        ${this.renderBlogOptions()}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="publishTitle" class="form-label">Title</label>
                                    <input type="text" class="form-control" id="publishTitle"
                                           value="${this.currentData.rewritten.title}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="publishContent" class="form-label">Content</label>
                                    <div class="content-editor" id="publishContent" contenteditable="true">
                                        ${this.currentData.rewritten.content}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="publishTags" class="form-label">Tags (comma-separated)</label>
                                    <input type="text" class="form-control" id="publishTags"
                                           value="${this.currentData.rewritten.tags.join(', ')}">
                                </div>

                                <div class="mb-3">
                                    <label for="publishPermalink" class="form-label">Custom Permalink (optional)</label>
                                    <input type="text" class="form-control" id="publishPermalink"
                                           value="${this.currentData.rewritten.permalink}">
                                </div>

                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="publishAsDraft">
                                    <label class="form-check-label" for="publishAsDraft">
                                        Save as draft
                                    </label>
                                </div>

                                <!-- AdSense ads are now inserted automatically for all published articles -->
                                <div class="alert alert-info py-2 mb-3">
                                    <i class="fas fa-ad me-1"></i>
                                    <small><strong>AdSense Integration:</strong> Ads will be automatically inserted into published articles (3-4 ads per article)</small>
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="insertCtaButton">
                                    <label class="form-check-label" for="insertCtaButton">
                                        <i class="fas fa-hand-pointer me-1"></i>Insert Call-to-Action button
                                        <small class="d-block text-muted">Add a CTA button at the end of the article with countdown popup</small>
                                    </label>
                                </div>

                                <!-- CTA Configuration (hidden by default) -->
                                <div id="ctaConfiguration" class="mt-3" style="display: none;">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-cog me-1"></i>CTA Button Configuration</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="ctaButtonText" class="form-label">Button Text</label>
                                                <input type="text" class="form-control" id="ctaButtonText" placeholder="e.g., Get Started Now, Learn More, Download Free Guide" value="Learn More">
                                            </div>
                                            <div class="mb-3">
                                                <label for="ctaTargetUrl" class="form-label">Target URL</label>
                                                <input type="url" class="form-control" id="ctaTargetUrl" placeholder="https://example.com/landing-page">
                                            </div>
                                            <div class="mb-3">
                                                <label for="ctaButtonColor" class="form-label">Button Color</label>
                                                <select class="form-select" id="ctaButtonColor">
                                                    <option value="primary">Primary Blue</option>
                                                    <option value="success">Success Green</option>
                                                    <option value="warning">Warning Orange</option>
                                                    <option value="danger">Danger Red</option>
                                                    <option value="info">Info Light Blue</option>
                                                    <option value="dark">Dark</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-info me-2" id="previewArticleBtn">
                                <i class="fas fa-eye me-1"></i>Preview Article
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="publishModalBtn">
                                <i class="fas fa-upload me-1"></i>Publish
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('modalContainer').innerHTML = modalHTML;

        // Add event listeners for blog selection
        document.querySelectorAll('.blog-option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.blog-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
            });
        });

        // Wait for Bootstrap to be available and show modal
        this.waitForBootstrap(() => {
            const modalElement = document.getElementById('publishModal');
            const modal = new bootstrap.Modal(modalElement);

            // Add proper focus management for accessibility
            modalElement.addEventListener('hide.bs.modal', () => {
                // Remove focus from any focused element inside the modal before hiding
                const focusedElement = modalElement.querySelector(':focus');
                if (focusedElement) {
                    focusedElement.blur();
                }
            });

            modal.show();

            // Add event listener for preview button
            const previewBtn = document.getElementById('previewArticleBtn');
            if (previewBtn) {
                previewBtn.addEventListener('click', () => generatePreview());
            }

            // Add event listener for publish button
            const publishModalBtn = document.getElementById('publishModalBtn');
            if (publishModalBtn) {
                publishModalBtn.addEventListener('click', () => {
                    this.publishToBlogger();
                });
            }
        });
    }

    renderBlogOptions() {
        if (this.currentData.blogs.length === 0) {
            return '<p class="text-muted">No blogs found. Please check your Blogger account.</p>';
        }

        return this.currentData.blogs.map(blog => `
            <div class="blog-option" data-blog-id="${blog.id}">
                <div class="blog-name">${blog.name}</div>
                <div class="blog-url">${blog.url}</div>
                <small class="text-muted">${blog.posts} posts</small>
            </div>
        `).join('');
    }

    async publishToBlogger() {
        const selectedBlog = document.querySelector('.blog-option.selected');
        if (!selectedBlog) {
            this.showAlert('Please select a blog', 'warning');
            return;
        }

        const blogId = selectedBlog.dataset.blogId;
        let title = document.getElementById('publishTitle').value.trim();
        let content = document.getElementById('publishContent').innerHTML;
        const tags = document.getElementById('publishTags').value.split(',').map(tag => tag.trim()).filter(tag => tag);
        const customUrl = document.getElementById('publishPermalink').value.trim();
        let metaDescription = this.currentData.rewritten?.metaDescription || '';
        const isDraft = document.getElementById('publishAsDraft').checked;
        const insertAdsenseAds = true; // Always insert ads automatically

        // Get CTA button configuration
        const insertCtaButton = document.getElementById('insertCtaButton').checked;
        let ctaConfig = null;
        if (insertCtaButton) {
            const ctaButtonText = document.getElementById('ctaButtonText').value.trim();
            const ctaTargetUrl = document.getElementById('ctaTargetUrl').value.trim();
            const ctaButtonColor = document.getElementById('ctaButtonColor').value;

            if (!ctaButtonText || !ctaTargetUrl) {
                this.showAlert('CTA button text and target URL are required when CTA is enabled', 'warning');
                return;
            }

            ctaConfig = {
                buttonText: ctaButtonText,
                targetUrl: ctaTargetUrl,
                buttonColor: ctaButtonColor
            };
        }

        if (!title || !content) {
            this.showAlert('Title and content are required', 'warning');
            return;
        }

        try {
            // Check if auto-humanization is enabled and process content
            if (await this.shouldAutoHumanize() && !isDraft) {
                console.log('Auto-humanization enabled, processing content before publishing...');

                const humanizeBtn = document.getElementById('humanizeContentBtn');
                if (humanizeBtn) {
                    humanizeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Auto-humanizing...';
                    humanizeBtn.disabled = true;
                }

                try {
                    const humanizeResponse = await fetch('/api/articles/humanize', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            content: content,
                            title: title
                        })
                    });

                    const humanizeResult = await humanizeResponse.json();

                    if (humanizeResult.success && humanizeResult.data) {
                        content = humanizeResult.data.content;
                        title = humanizeResult.data.title || title;
                        metaDescription = humanizeResult.data.metaDescription || metaDescription;

                        console.log('Content auto-humanized successfully before publishing');
                        this.showAlert('Content auto-humanized to reduce AI detection', 'info');
                    } else {
                        console.warn('Auto-humanization failed, proceeding with original content');
                    }
                } catch (humanizeError) {
                    console.error('Auto-humanization error:', humanizeError);
                    this.showAlert('Auto-humanization failed, publishing original content', 'warning');
                } finally {
                    if (humanizeBtn) {
                        humanizeBtn.innerHTML = '<i class="fas fa-user-edit me-1"></i>Make it Human-like';
                        humanizeBtn.disabled = false;
                    }
                }
            }
            const response = await fetch('/api/articles/publish', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    blogId,
                    title,
                    content,
                    labels: tags,
                    customUrl,
                    metaDescription,
                    isDraft,
                    insertAdsenseAds,
                    insertCtaButton,
                    ctaConfig
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert(`Article ${isDraft ? 'saved as draft' : 'published'} successfully!`, 'success');
                this.addToRecentActivity('Published article', title);

                // Store published article data for Telegram sharing
                this.lastPublishedArticle = {
                    title,
                    content,
                    url: data.data.url,
                    labels: tags,
                    isDraft
                };

                console.log('Publishing successful, article data:', {
                    isDraft,
                    hasUrl: !!data.data.url,
                    url: data.data.url,
                    lastPublishedArticle: this.lastPublishedArticle
                });

                // Close modal and show Telegram sharing option if not draft
                this.waitForBootstrap(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('publishModal'));
                    if (modal) {
                        // Add event listener for when modal is fully hidden
                        const modalElement = document.getElementById('publishModal');
                        modalElement.addEventListener('hidden.bs.modal', () => {
                            // Show Telegram sharing option for published articles (not drafts)
                            if (!isDraft && data.data.url) {
                                console.log('Showing Telegram sharing option...');
                                setTimeout(() => {
                                    this.showTelegramSharingOption();
                                }, 500);
                            } else {
                                console.log('Not showing Telegram option:', { isDraft, hasUrl: !!data.data.url });
                            }
                        }, { once: true }); // Only listen once

                        modal.hide();
                    }
                });
            } else {
                this.showAlert(`Publishing failed: ${data.error}`, 'danger');
            }
        } catch (error) {
            console.error('Publishing error:', error);
            this.showAlert('Error publishing article', 'danger');
        }
    }

    showSettings() {
        const modalHTML = `
            <div class="modal fade" id="settingsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-cog me-2"></i>Settings
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="settings-section">
                                <h6>API Configuration</h6>
                                <div class="mb-3">
                                    <label for="geminiApiKey" class="form-label">Gemini API Key</label>
                                    <input type="password" class="form-control" id="geminiApiKey"
                                           placeholder="Enter your Gemini API key">
                                    <div class="form-text">Your API key is stored securely and used only for content rewriting.</div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h6>Processing Options</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoScrape" checked>
                                    <label class="form-check-label" for="autoScrape">
                                        Auto-validate URLs before scraping
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showPreview" checked>
                                    <label class="form-check-label" for="showPreview">
                                        Show content preview after processing
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="saveSettingsBtn">
                                <i class="fas fa-save me-1"></i>Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('modalContainer').innerHTML = modalHTML;

        // Wait for Bootstrap to be available and show modal
        this.waitForBootstrap(() => {
            const modalElement = document.getElementById('settingsModal');
            const modal = new bootstrap.Modal(modalElement);

            // Add proper focus management for accessibility
            modalElement.addEventListener('hide.bs.modal', () => {
                // Remove focus from any focused element inside the modal before hiding
                const focusedElement = modalElement.querySelector(':focus');
                if (focusedElement) {
                    focusedElement.blur();
                }
            });

            modal.show();

            // Add event listener for save settings button
            const saveSettingsBtn = document.getElementById('saveSettingsBtn');
            if (saveSettingsBtn) {
                saveSettingsBtn.addEventListener('click', () => {
                    this.saveSettings();
                });
            }
        });
    }

    async saveSettings() {
        const geminiApiKey = document.getElementById('geminiApiKey').value.trim();

        if (geminiApiKey) {
            try {
                const response = await fetch('/api/config/gemini-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ apiKey: geminiApiKey })
                });

                const data = await response.json();

                if (data.success) {
                    this.showAlert('Settings saved successfully!', 'success');
                } else {
                    this.showAlert(`Failed to save settings: ${data.error}`, 'danger');
                }
            } catch (error) {
                console.error('Settings save error:', error);
                this.showAlert('Error saving settings', 'danger');
            }
        }

        // Close modal
        this.waitForBootstrap(() => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
            if (modal) {
                modal.hide();
            }
        });
    }

    waitForBootstrap(callback) {
        if (typeof bootstrap !== 'undefined') {
            callback();
        } else {
            // Wait for Bootstrap to load
            setTimeout(() => this.waitForBootstrap(callback), 100);
        }
    }

    // Telegram sharing functionality
    showTelegramSharingOption() {
        console.log('showTelegramSharingOption called, lastPublishedArticle:', this.lastPublishedArticle);

        if (!this.lastPublishedArticle) {
            console.log('No lastPublishedArticle found, returning');
            return;
        }

        const alertHTML = `
            <div class="alert alert-info alert-dismissible fade show telegram-share-alert" role="alert" id="telegramShareAlert">
                <div class="d-flex align-items-center">
                    <i class="fab fa-telegram-plane me-2 fs-4 text-primary"></i>
                    <div class="flex-grow-1">
                        <strong>Share to Telegram!</strong>
                        <p class="mb-2">Would you like to share this article to your Telegram channel?</p>
                        <button type="button" class="btn btn-primary btn-sm me-2" id="telegramShareBtn">
                            <i class="fab fa-telegram-plane me-1"></i>Share to Telegram
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="telegramMaybeLaterBtn">
                            Maybe Later
                        </button>
                    </div>
                    <button type="button" class="btn-close" id="telegramCloseBtn" aria-label="Close"></button>
                </div>
            </div>
        `;

        // Insert the alert at the top of the main content
        const mainContent = document.querySelector('.col-lg-8');
        console.log('Main content element found:', !!mainContent);

        if (mainContent) {
            mainContent.insertAdjacentHTML('afterbegin', alertHTML);
            console.log('Telegram alert inserted into main content');
        } else {
            // Fallback: insert after alert container
            const alertContainer = document.getElementById('alertContainer');
            console.log('Fallback: alert container found:', !!alertContainer);

            if (alertContainer) {
                alertContainer.insertAdjacentHTML('afterend', alertHTML);
                console.log('Telegram alert inserted after alert container');
            } else {
                console.error('Could not find suitable container for Telegram alert');
            }
        }

        // Add event listeners for the Telegram buttons
        this.attachTelegramEventListeners();
    }

    attachTelegramEventListeners() {
        // Share to Telegram button
        const shareBtn = document.getElementById('telegramShareBtn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.showTelegramPreview();
            });
        }

        // Maybe Later button
        const maybeLaterBtn = document.getElementById('telegramMaybeLaterBtn');
        if (maybeLaterBtn) {
            maybeLaterBtn.addEventListener('click', () => {
                this.dismissTelegramAlert();
            });
        }

        // Close button
        const closeBtn = document.getElementById('telegramCloseBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.dismissTelegramAlert();
            });
        }
    }

    dismissTelegramAlert() {
        const alert = document.querySelector('.telegram-share-alert');
        if (alert) {
            alert.remove();
        }
    }

    async showTelegramPreview() {
        if (!this.lastPublishedArticle) {
            this.showAlert('No article data available for sharing', 'warning');
            return;
        }

        try {
            // Generate preview
            const response = await fetch('/api/articles/telegram/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.lastPublishedArticle)
            });

            const data = await response.json();

            if (data.success) {
                this.showTelegramModal(data.data);
            } else {
                this.showAlert(`Failed to generate preview: ${data.error}`, 'danger');
            }
        } catch (error) {
            console.error('Telegram preview error:', error);
            this.showAlert('Error generating Telegram preview', 'danger');
        }
    }

    showTelegramModal(previewData) {
        const modalHTML = `
            <div class="modal fade" id="telegramModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fab fa-telegram-plane me-2"></i>Share to Telegram
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-info-circle me-2"></i>Channel Info</h6>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <p class="mb-1"><strong>Channel:</strong> ${previewData.groupName}</p>
                                            <p class="mb-1"><strong>Type:</strong> ${previewData.isJobRelated ? 'Job-related' : 'General'} article</p>
                                            <p class="mb-0"><strong>Message Length:</strong> ${previewData.messageLength} characters</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-eye me-2"></i>Preview</h6>
                                    <div class="card">
                                        <div class="card-body telegram-preview">
                                            <div class="telegram-message">
                                                ${this.formatTelegramPreview(previewData.message)}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h6><i class="fas fa-edit me-2"></i>Edit Message</h6>
                                <textarea class="form-control" id="telegramMessage" rows="8" placeholder="Edit your Telegram message...">${previewData.message}</textarea>
                                <div class="form-text">You can edit the message before sending. Telegram supports Markdown formatting.</div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="sendToTelegramBtn">
                                <i class="fab fa-telegram-plane me-1"></i>Send to Telegram
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('telegramModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show modal
        this.waitForBootstrap(() => {
            const modalElement = document.getElementById('telegramModal');
            const modal = new bootstrap.Modal(modalElement);

            // Add proper focus management for accessibility
            modalElement.addEventListener('hide.bs.modal', () => {
                // Remove focus from any focused element inside the modal before hiding
                const focusedElement = modalElement.querySelector(':focus');
                if (focusedElement) {
                    focusedElement.blur();
                }
            });

            modal.show();

            // Add event listener for send button
            const sendBtn = document.getElementById('sendToTelegramBtn');
            if (sendBtn) {
                sendBtn.addEventListener('click', () => {
                    this.sendToTelegram();
                });
            }
        });

        // Dismiss the sharing alert
        this.dismissTelegramAlert();
    }

    formatTelegramPreview(message) {
        // Convert Markdown to HTML for preview
        return message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
            .replace(/\n/g, '<br>');
    }

    async sendToTelegram() {
        const messageTextarea = document.getElementById('telegramMessage');
        if (!messageTextarea) {
            this.showAlert('Message not found', 'danger');
            return;
        }

        const message = messageTextarea.value.trim();
        if (!message) {
            this.showAlert('Message cannot be empty', 'warning');
            return;
        }

        try {
            // Show loading state
            const sendButton = document.querySelector('#telegramModal .btn-primary');
            const originalText = sendButton.innerHTML;
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sending...';
            sendButton.disabled = true;

            const response = await fetch('/api/articles/telegram/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('Article shared to Telegram successfully!', 'success');

                // Close modal
                this.waitForBootstrap(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('telegramModal'));
                    if (modal) {
                        modal.hide();
                    }
                });
            } else {
                this.showAlert(`Failed to send to Telegram: ${data.error}`, 'danger');

                // Restore button
                sendButton.innerHTML = originalText;
                sendButton.disabled = false;
            }
        } catch (error) {
            console.error('Telegram send error:', error);
            this.showAlert('Error sending to Telegram', 'danger');

            // Restore button
            const sendButton = document.querySelector('#telegramModal .btn-primary');
            if (sendButton) {
                sendButton.innerHTML = '<i class="fab fa-telegram-plane me-1"></i>Send to Telegram';
                sendButton.disabled = false;
            }
        }
    }

    // Image upload functionality
    initializeImageUpload() {
        // Insert Image button in content editor
        const insertImageBtn = document.getElementById('insertImageBtn');
        if (insertImageBtn) {
            insertImageBtn.addEventListener('click', () => {
                this.showImageUploadModal();
            });
        }

        // Insert Ads button in content editor
        const insertAdsBtn = document.getElementById('insertAdsBtn');
        if (insertAdsBtn) {
            insertAdsBtn.addEventListener('click', () => {
                this.insertAdsIntoContent();
            });
        }

        // Humanize Content button in content editor
        const humanizeContentBtn = document.getElementById('humanizeContentBtn');
        if (humanizeContentBtn) {
            humanizeContentBtn.addEventListener('click', () => {
                this.humanizeCurrentContent();
            });
        }

        // Image file input change handler
        const imageFileInput = document.getElementById('imageFile');
        if (imageFileInput) {
            imageFileInput.addEventListener('change', (e) => {
                this.handleImageFileSelect(e);
            });
        }

        // Upload and insert button
        const uploadImageBtn = document.getElementById('uploadImageBtn');
        if (uploadImageBtn) {
            uploadImageBtn.addEventListener('click', () => {
                this.uploadAndInsertImage();
            });
        }
    }

    showImageUploadModal() {
        // Reset form
        const form = document.getElementById('imageUploadForm');
        if (form) {
            form.reset();
        }

        // Hide preview
        const previewContainer = document.getElementById('imagePreviewContainer');
        if (previewContainer) {
            previewContainer.style.display = 'none';
        }

        // Show modal
        this.waitForBootstrap(() => {
            const modalElement = document.getElementById('imageUploadModal');
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: true,
                keyboard: true,
                focus: true
            });

            // Add proper focus management for accessibility
            modalElement.addEventListener('hide.bs.modal', () => {
                // Remove focus from any focused element inside the modal before hiding
                const focusedElement = modalElement.querySelector(':focus');
                if (focusedElement) {
                    focusedElement.blur();
                }
            });

            modal.show();
        });
    }

    handleImageFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            this.showAlert('Invalid file type. Please select a JPG, PNG, GIF, or WebP image.', 'warning');
            event.target.value = '';
            return;
        }

        // Validate file size (5MB)
        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
            this.showAlert('File size too large. Maximum size is 5MB.', 'warning');
            event.target.value = '';
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = (e) => {
            const preview = document.getElementById('imagePreview');
            const previewContainer = document.getElementById('imagePreviewContainer');

            if (preview && previewContainer) {
                preview.src = e.target.result;
                previewContainer.style.display = 'block';
            }
        };
        reader.readAsDataURL(file);

        // Auto-generate alt text from filename
        const altTextInput = document.getElementById('imageAltText');
        if (altTextInput && !altTextInput.value) {
            const filename = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
            const altText = filename.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            altTextInput.value = altText;
        }
    }

    async uploadAndInsertImage() {
        const fileInput = document.getElementById('imageFile');
        const altTextInput = document.getElementById('imageAltText');
        const widthSelect = document.getElementById('imageWidth');
        const alignmentSelect = document.getElementById('imageAlignment');
        const uploadBtn = document.getElementById('uploadImageBtn');

        if (!fileInput.files[0]) {
            this.showAlert('Please select an image file', 'warning');
            return;
        }

        const altText = altTextInput.value.trim();
        if (!altText) {
            this.showAlert('Please provide alt text for the image', 'warning');
            return;
        }

        try {
            // Show loading state
            const originalText = uploadBtn.innerHTML;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
            uploadBtn.disabled = true;

            // Create form data
            const formData = new FormData();
            formData.append('image', fileInput.files[0]);
            formData.append('altText', altText);

            // Upload image
            const response = await fetch('/api/images/upload', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                // Generate HTML with options
                const width = widthSelect.value;
                const alignment = alignmentSelect.value;

                const options = {
                    width: width,
                    className: 'img-fluid'
                };

                // Add alignment styles
                if (alignment !== 'none') {
                    if (alignment === 'center') {
                        options.style = 'display: block; margin: 0 auto;';
                    } else {
                        options.style = `float: ${alignment}; margin: 10px;`;
                    }
                }

                const htmlResponse = await fetch('/api/images/generate-html', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        imageData: data.data,
                        options: options
                    })
                });

                const htmlData = await htmlResponse.json();

                if (htmlData.success) {
                    // Insert HTML into content editor
                    this.insertImageIntoContent(htmlData.data.html);

                    // Close modal properly
                    this.waitForBootstrap(() => {
                        const modalElement = document.getElementById('imageUploadModal');
                        const modal = bootstrap.Modal.getInstance(modalElement);
                        if (modal) {
                            modalElement.addEventListener('hidden.bs.modal', () => {
                                this.showAlert('Image uploaded and inserted successfully!', 'success');
                            }, { once: true });
                            modal.hide();
                        } else {
                            this.showAlert('Image uploaded and inserted successfully!', 'success');
                        }
                    });
                } else {
                    throw new Error(htmlData.error || 'Failed to generate HTML');
                }
            } else {
                throw new Error(data.error || 'Upload failed');
            }

        } catch (error) {
            console.error('Image upload error:', error);
            this.showAlert(`Failed to upload image: ${error.message}`, 'danger');
        } finally {
            // Restore button
            uploadBtn.innerHTML = '<i class="fas fa-upload me-1"></i>Upload & Insert';
            uploadBtn.disabled = false;
        }
    }

    insertImageIntoContent(imageHtml) {
        const contentTextarea = document.getElementById('editContent');
        if (!contentTextarea) {
            console.error('Content textarea not found');
            return;
        }

        // Get current cursor position
        const cursorPos = contentTextarea.selectionStart;
        const textBefore = contentTextarea.value.substring(0, cursorPos);
        const textAfter = contentTextarea.value.substring(contentTextarea.selectionEnd);

        // Insert image HTML with proper spacing
        const imageWithSpacing = `\n\n${imageHtml}\n\n`;
        contentTextarea.value = textBefore + imageWithSpacing + textAfter;

        // Set cursor position after inserted image
        const newCursorPos = cursorPos + imageWithSpacing.length;
        contentTextarea.setSelectionRange(newCursorPos, newCursorPos);
        contentTextarea.focus();
    }

    // Humanization functionality
    async insertAdsIntoContent() {
        const contentTextarea = document.getElementById('editContent');

        if (!contentTextarea || !contentTextarea.value.trim()) {
            this.showAlert('Please enter some content to humanize', 'warning');
            return;
        }

        const content = contentTextarea.value.trim();

        if (!content) {
            alert('Please add some content before inserting ads.');
            return;
        }

        // Show loading state
        const insertAdsBtn = document.getElementById('insertAdsBtn');
        const originalText = insertAdsBtn.innerHTML;
        insertAdsBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Inserting Ads...';
        insertAdsBtn.disabled = true;

        try {
            console.log('Inserting AdSense ads into content...');

            const response = await fetch('/api/articles/insert-ads', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: content
                })
            });

            const result = await response.json();

            if (result.success) {
                contentTextarea.value = result.content;
                console.log('AdSense ads inserted successfully:', {
                    adsInserted: result.adsInserted,
                    message: result.message
                });

                // Show success message
                alert(`Successfully inserted ${result.adsInserted} AdSense ads into the content!`);
            } else {
                console.error('Failed to insert ads:', result.error);
                alert('Failed to insert ads: ' + result.error);
            }

        } catch (error) {
            console.error('Error inserting ads:', error);
            alert('Error inserting ads. Please try again.');
        } finally {
            // Restore button state
            insertAdsBtn.innerHTML = originalText;
            insertAdsBtn.disabled = false;
        }
    }

    async humanizeCurrentContent() {
        const contentTextarea = document.getElementById('editContent');
        const titleInput = document.getElementById('editTitle');

        if (!contentTextarea) {
            console.error('Content textarea not found');
            return;
        }

        const content = contentTextarea.value.trim();
        const title = titleInput ? titleInput.value.trim() : '';

        // Show loading state
        const humanizeBtn = document.getElementById('humanizeContentBtn');
        const originalText = humanizeBtn.innerHTML;
        humanizeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Humanizing...';
        humanizeBtn.disabled = true;

        try {
            console.log('Starting content humanization...');

            const response = await fetch('/api/articles/humanize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: content,
                    title: title
                })
            });

            const result = await response.json();

            if (result.success && result.data) {
                // Update the content with humanized version
                contentTextarea.value = result.data.content;

                // Update title if provided
                if (result.data.title && titleInput) {
                    titleInput.value = result.data.title;
                }

                // Update meta description if available
                const metaDescInput = document.getElementById('editMetaDescription');
                if (result.data.metaDescription && metaDescInput) {
                    metaDescInput.value = result.data.metaDescription;
                }

                // Show success message with humanization notes
                const notes = result.data.humanizationNotes || 'Content has been humanized to reduce AI detection';
                this.showAlert(`Content humanized successfully! ${notes}`, 'success');

                console.log('Humanization completed:', {
                    originalWords: result.data.originalWordCount,
                    humanizedWords: result.data.humanizedWordCount,
                    notes: notes
                });
            } else {
                throw new Error(result.error || 'Humanization failed');
            }
        } catch (error) {
            console.error('Humanization error:', error);
            this.showAlert(`Humanization failed: ${error.message}`, 'danger');
        } finally {
            // Restore button state
            humanizeBtn.innerHTML = originalText;
            humanizeBtn.disabled = false;
        }
    }

    async shouldAutoHumanize() {
        const autoHumanizeCheck = document.getElementById('autoHumanizeCheck');
        return autoHumanizeCheck && autoHumanizeCheck.checked;
    }
}

// Global functions removed - using proper event listeners instead

// Generate article preview with all features
async function generatePreview() {
    try {
        const contentElement = document.getElementById('content') || document.getElementById('publishContent');
        const titleElement = document.getElementById('title') || document.getElementById('publishTitle');

        if (!contentElement || !titleElement) {
            alert('Content or title elements not found. Please try again.');
            return;
        }

        const content = contentElement.value || contentElement.innerHTML;
        const title = titleElement.value || titleElement.textContent;

        if (!content.trim()) {
            alert('Please add some content to preview');
            return;
        }

        // Get current settings from the publish modal
        const insertAdsenseAds = true; // Always insert ads automatically
        const insertCtaButtonElement = document.getElementById('insertCtaButton');
        const insertCtaButton = insertCtaButtonElement ? insertCtaButtonElement.checked : false;

        let ctaConfig = null;
        if (insertCtaButton) {
            const ctaButtonTextElement = document.getElementById('ctaButtonText');
            const ctaTargetUrlElement = document.getElementById('ctaTargetUrl');
            const ctaButtonColorElement = document.getElementById('ctaButtonColor');

            ctaConfig = {
                buttonText: ctaButtonTextElement ? ctaButtonTextElement.value.trim() || 'Click Here' : 'Click Here',
                targetUrl: ctaTargetUrlElement ? ctaTargetUrlElement.value.trim() || 'https://example.com' : 'https://example.com',
                buttonColor: ctaButtonColorElement ? ctaButtonColorElement.value || '#007bff' : '#007bff'
            };
        }

        // Show loading state
        const previewBtn = document.getElementById('previewArticleBtn');
        if (!previewBtn) {
            alert('Preview button not found');
            return;
        }

        const originalText = previewBtn.innerHTML;
        previewBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating Preview...';
        previewBtn.disabled = true;

        // Generate preview
        const response = await fetch('/api/articles/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                content,
                title,
                insertAdsenseAds,
                insertCtaButton,
                ctaConfig
            })
        });

        const result = await response.json();

        // Restore button state
        previewBtn.innerHTML = originalText;
        previewBtn.disabled = false;

        if (result.success) {
            showPreviewModal(result.data);
        } else {
            alert('Failed to generate preview: ' + result.error);
        }

    } catch (error) {
        console.error('Error generating preview:', error);
        alert('Error generating preview. Please try again.');

        // Restore button state
        const previewBtn = document.getElementById('previewArticleBtn');
        if (previewBtn) {
            previewBtn.innerHTML = '<i class="fas fa-eye me-1"></i>Preview Article';
            previewBtn.disabled = false;
        }
    }
}

// Show preview modal with generated content
function showPreviewModal(previewData) {
    const modalHTML = `
        <div class="modal fade" id="previewModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-eye me-2"></i>Article Preview
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="preview-content" style="border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: white; max-height: 600px; overflow-y: auto;">
                                    <h2 style="color: #333; margin-bottom: 20px;">${previewData.title}</h2>
                                    ${previewData.previewContent}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="preview-info" style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                    <h6><i class="fas fa-info-circle me-2"></i>Preview Information</h6>
                                    <div class="mb-3">
                                        <small class="text-muted">Content Length:</small><br>
                                        <strong>${previewData.previewInfo.originalContentLength} characters</strong>
                                    </div>

                                    ${previewData.previewInfo.features.length > 0 ? `
                                        <div class="mb-3">
                                            <small class="text-muted">Features Included:</small><br>
                                            ${previewData.previewInfo.features.map(feature =>
                                                `<span class="badge bg-primary me-1 mb-1">${feature}</span>`
                                            ).join('')}
                                        </div>
                                    ` : ''}

                                    ${previewData.previewInfo.adsInserted > 0 ? `
                                        <div class="mb-3">
                                            <small class="text-muted">AdSense Ads:</small><br>
                                            <strong class="text-success">${previewData.previewInfo.adsInserted} ads will be inserted</strong>
                                        </div>
                                    ` : ''}

                                    ${previewData.previewInfo.ctaButtonAdded ? `
                                        <div class="mb-3">
                                            <small class="text-muted">CTA Button:</small><br>
                                            <strong class="text-info">Call-to-action button included</strong>
                                        </div>
                                    ` : ''}

                                    ${previewData.previewInfo.imagesProcessed > 0 ? `
                                        <div class="mb-3">
                                            <small class="text-muted">Images:</small><br>
                                            <strong class="text-warning">${previewData.previewInfo.imagesProcessed} image(s) will be uploaded</strong>
                                        </div>
                                    ` : ''}

                                    <div class="alert alert-info mt-3">
                                        <small><i class="fas fa-lightbulb me-1"></i>
                                        This is a preview showing how your article will look when published.
                                        Colored borders indicate where features will be added.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close Preview</button>
                        <button type="button" class="btn btn-primary" id="publishFromPreviewBtn">
                            <i class="fas fa-upload me-1"></i>Looks Good, Publish Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing preview modal if any
    const existingModal = document.getElementById('previewModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modalElement = document.getElementById('previewModal');
    const bootstrapModal = new bootstrap.Modal(modalElement);

    // Add proper focus management for accessibility
    modalElement.addEventListener('hide.bs.modal', () => {
        // Remove focus from any focused element inside the modal before hiding
        const focusedElement = modalElement.querySelector(':focus');
        if (focusedElement) {
            focusedElement.blur();
        }
    });

    bootstrapModal.show();

    // Add event listener for publish from preview button
    const publishFromPreviewBtn = document.getElementById('publishFromPreviewBtn');
    if (publishFromPreviewBtn) {
        publishFromPreviewBtn.addEventListener('click', () => {
            bootstrapModal.hide();
            // Trigger the publish button in the main modal
            setTimeout(() => {
                const publishBtn = document.getElementById('publishModalBtn');
                if (publishBtn) {
                    publishBtn.click();
                }
            }, 300); // Wait for modal to hide
        });
    }

    // Clean up modal when hidden
    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new ArticleRewriter();
});
