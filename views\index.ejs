<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-edit me-2"></i>Article Rewriter
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="settingsBtn">
                            <i class="fas fa-cog me-1"></i>Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/adsense-settings">
                            <i class="fas fa-ad me-1"></i>AdSense
                        </a>
                    </li>
                    <% if (user) { %>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i><%= user.displayName %>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/auth/logout">Logout</a></li>
                            </ul>
                        </li>
                    <% } else { %>
                        <li class="nav-item">
                            <a class="nav-link" href="/auth/google">
                                <i class="fab fa-google me-1"></i>Login with Google
                            </a>
                        </li>
                    <% } %>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Alert Container -->
        <div id="alertContainer"></div>

        <!-- Authentication Notice -->
        <% if (!user) { %>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Please <a href="/auth/google" class="alert-link">login with Google</a> to access Blogger publishing features.
            </div>
        <% } %>

        <!-- Main Form -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-link me-2"></i>Article Processing
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="articleForm">
                            <!-- URL Input -->
                            <div class="mb-3">
                                <label for="articleUrl" class="form-label">Article URL</label>
                                <div class="input-group">
                                    <input type="url" class="form-control" id="articleUrl" 
                                           placeholder="https://example.com/article" required>
                                    <button class="btn btn-outline-secondary" type="button" id="validateUrlBtn">
                                        <i class="fas fa-check"></i> Validate
                                    </button>
                                </div>
                                <div class="form-text">Enter the URL of the article you want to rewrite</div>
                            </div>

                            <!-- Article Type -->
                            <div class="mb-3">
                                <label for="articleType" class="form-label">Article Type</label>
                                <select class="form-select" id="articleType">
                                    <option value="general">General Article</option>
                                    <option value="news">News Article</option>
                                    <option value="blog">Blog Post</option>
                                    <option value="job">Job Posting</option>
                                    <option value="product">Product Description</option>
                                    <option value="review">Review Article</option>
                                    <option value="tutorial">Tutorial/How-to</option>
                                    <option value="research">Research Article</option>
                                    <option value="press">Press Release</option>
                                    <option value="interview">Interview</option>
                                </select>
                            </div>

                            <!-- Custom Prompt -->
                            <div class="mb-3">
                                <label for="customPrompt" class="form-label">Custom Instructions (Optional)</label>
                                <textarea class="form-control" id="customPrompt" rows="3" 
                                          placeholder="Add any specific instructions for the AI rewriter..."></textarea>
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                                <button type="button" class="btn btn-primary" id="scrapeBtn">
                                    <i class="fas fa-download me-1"></i>Scrape Content
                                </button>
                                <button type="button" class="btn btn-success" disabled id="rewriteBtn">
                                    <i class="fas fa-magic me-1"></i>Rewrite with AI
                                </button>
                                <% if (user) { %>
                                    <button type="button" class="btn btn-info" disabled id="publishBtn">
                                        <i class="fas fa-upload me-1"></i>Publish to Blogger
                                    </button>
                                <% } %>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Progress Indicator -->
                <div id="progressContainer" class="mt-3" style="display: none;">
                    <div class="card">
                        <div class="card-body">
                            <h6>Processing Progress</h6>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="progressText" class="mt-2 text-muted">Initializing...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>How it Works
                        </h6>
                    </div>
                    <div class="card-body">
                        <ol class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-1 text-primary me-2"></i>
                                Enter the URL of the article you want to rewrite
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-2 text-primary me-2"></i>
                                Select the appropriate article type
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-3 text-primary me-2"></i>
                                Click "Scrape Content" to extract the article
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-4 text-primary me-2"></i>
                                Use AI to rewrite the content for SEO optimization
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-5 text-primary me-2"></i>
                                Edit and publish to your Blogger account
                            </li>
                        </ol>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i>Recent Activity
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="recentActivity">
                            <p class="text-muted">No recent activity</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Display Area -->
        <div id="contentArea" class="mt-4" style="display: none;">
            <!-- Original Content -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-file-alt me-2"></i>Original Content
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="originalContent">
                                <!-- Original content will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rewritten Content -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>Rewritten Content
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="rewrittenContent">
                                <!-- Rewritten content will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Editor Modal -->
    <div class="modal fade" id="contentEditorModal" tabindex="-1" aria-labelledby="contentEditorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contentEditorModalLabel">
                        <i class="fas fa-edit me-2"></i>Edit Content
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="contentEditorForm">
                        <div class="mb-3">
                            <label for="editTitle" class="form-label">Title</label>
                            <input type="text" class="form-control" id="editTitle" required>
                        </div>

                        <div class="mb-3">
                            <label for="editContent" class="form-label">Content</label>
                            <div class="content-editor-toolbar mb-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="insertImageBtn">
                                    <i class="fas fa-image me-1"></i>Insert Image
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm ms-2" id="insertAdsBtn">
                                    <i class="fas fa-ad me-1"></i>Insert Ads (3-4)
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm ms-2" id="humanizeContentBtn">
                                    <i class="fas fa-user-edit me-1"></i>Make it Human-like
                                </button>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="autoHumanizeCheck">
                                <label class="form-check-label" for="autoHumanizeCheck">
                                    <i class="fas fa-robot me-1"></i>Automatically humanize content before publishing (reduces AI detection)
                                </label>
                            </div>
                            <textarea class="form-control" id="editContent" rows="15" required></textarea>
                            <div class="form-text">HTML formatting is preserved. Use the toolbar to insert images.</div>
                        </div>

                        <div class="mb-3">
                            <label for="editMetaDescription" class="form-label">Meta Description</label>
                            <textarea class="form-control" id="editMetaDescription" rows="3" maxlength="160"></textarea>
                            <div class="form-text">Recommended: 150-160 characters for SEO</div>
                        </div>

                        <div class="mb-3">
                            <label for="editTags" class="form-label">Tags</label>
                            <input type="text" class="form-control" id="editTags" placeholder="Enter tags separated by commas">
                            <div class="form-text">Separate tags with commas (e.g., technology, AI, programming)</div>
                        </div>

                        <div class="mb-3">
                            <label for="editPermalink" class="form-label">Permalink</label>
                            <input type="text" class="form-control" id="editPermalink">
                            <div class="form-text">URL-friendly version of the title</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveContentBtn">
                        <i class="fas fa-save me-1"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Upload Modal -->
    <div class="modal fade" id="imageUploadModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-image me-2"></i>Insert Image
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="imageUploadForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="imageFile" class="form-label">Select Image</label>
                            <input type="file" class="form-control" id="imageFile" name="image"
                                   accept="image/jpeg,image/png,image/gif,image/webp" required>
                            <div class="form-text">Supported formats: JPG, PNG, GIF, WebP. Maximum size: 5MB.</div>
                        </div>

                        <div class="mb-3">
                            <label for="imageAltText" class="form-label">Alt Text</label>
                            <input type="text" class="form-control" id="imageAltText" name="altText"
                                   placeholder="Describe the image for accessibility">
                            <div class="form-text">Important for SEO and accessibility</div>
                        </div>

                        <div class="mb-3" id="imagePreviewContainer" style="display: none;">
                            <label class="form-label">Preview</label>
                            <div class="border rounded p-2">
                                <img id="imagePreview" src="" alt="Preview" class="img-fluid" style="max-height: 200px;">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Image Options</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="imageWidth" class="form-label">Width</label>
                                    <select class="form-select" id="imageWidth">
                                        <option value="auto">Auto</option>
                                        <option value="100%">Full Width</option>
                                        <option value="75%">75%</option>
                                        <option value="50%">50%</option>
                                        <option value="25%">25%</option>
                                        <option value="300px">300px</option>
                                        <option value="500px">500px</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="imageAlignment" class="form-label">Alignment</label>
                                    <select class="form-select" id="imageAlignment">
                                        <option value="none">None</option>
                                        <option value="left">Left</option>
                                        <option value="center">Center</option>
                                        <option value="right">Right</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="uploadImageBtn">
                        <i class="fas fa-upload me-1"></i>Upload & Insert
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>
