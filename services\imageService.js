/**
 * Image Service for handling image uploads and processing
 */

const fs = require('fs').promises;
const path = require('path');
const multer = require('multer');
const axios = require('axios');

class ImageService {
    constructor() {
        this.maxFileSize = 5 * 1024 * 1024; // 5MB
        this.allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        this.allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

        // Note: No longer using local storage - images uploaded directly to ImgBB
        console.log('ImageService initialized with direct ImgBB upload (no local storage)');
    }

    getMulterConfig() {
        // Use memory storage instead of disk storage to avoid local file storage
        const storage = multer.memoryStorage();

        const fileFilter = (req, file, cb) => {
            const ext = path.extname(file.originalname).toLowerCase();
            
            if (this.allowedMimeTypes.includes(file.mimetype) && 
                this.allowedExtensions.includes(ext)) {
                cb(null, true);
            } else {
                cb(new Error(`Invalid file type. Allowed types: ${this.allowedExtensions.join(', ')}`), false);
            }
        };

        return multer({
            storage,
            fileFilter,
            limits: {
                fileSize: this.maxFileSize,
                files: 1
            }
        });
    }

    async processUploadedImage(file, altText = '') {
        try {
            if (!file) {
                throw new Error('No file provided');
            }

            // Generate unique filename
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
            const ext = path.extname(file.originalname).toLowerCase();
            const filename = `image-${uniqueSuffix}${ext}`;

            // Create image data object with memory buffer
            const imageData = {
                id: path.basename(filename, path.extname(filename)),
                filename: filename,
                originalName: file.originalname,
                buffer: file.buffer, // Use buffer instead of file path
                size: file.size,
                mimeType: file.mimetype,
                altText: altText || `Image: ${file.originalname}`,
                uploadedAt: new Date().toISOString(),
                url: null // Will be set after upload to ImgBB
            };

            // Upload directly to ImgBB if API key is available
            if (process.env.IMGBB_API_KEY) {
                console.log('Uploading image directly to ImgBB...');
                const publicUrl = await this.uploadBufferToImgBB(file.buffer, filename);
                imageData.url = publicUrl;
                imageData.publicUrl = publicUrl;

                console.log('Image uploaded directly to ImgBB:', {
                    filename: imageData.filename,
                    size: imageData.size,
                    mimeType: imageData.mimeType,
                    publicUrl: publicUrl
                });
            } else {
                // Fallback: create a temporary local URL (not recommended for production)
                imageData.url = `/uploads/${filename}`;
                console.warn('ImgBB API key not found. Using temporary local URL. Image will not be accessible externally.');
            }

            return imageData;
        } catch (error) {
            console.error('Image processing failed:', error);
            throw error;
        }
    }

    async deleteImage(filename) {
        try {
            const filePath = path.join(this.uploadDir, filename);
            await fs.unlink(filePath);
            console.log('Image deleted successfully:', filename);
            return true;
        } catch (error) {
            console.error('Failed to delete image:', error);
            return false;
        }
    }

    generateImageHtml(imageData, options = {}) {
        const {
            width = 'auto',
            height = 'auto',
            className = 'img-fluid',
            style = ''
        } = options;

        // For Blogger, we'll use the local URL initially
        // The publishing process will handle uploading to Blogger
        return `<img src="${imageData.url}" alt="${imageData.altText}" class="${className}" style="width: ${width}; height: ${height}; ${style}" data-image-id="${imageData.id}" data-filename="${imageData.filename}">`;
    }

    async uploadToBlogger(imageData, bloggerService, blogId) {
        try {
            // Read the image file
            const imageBuffer = await fs.readFile(imageData.path);
            
            // Convert to base64 for Blogger API
            const base64Image = imageBuffer.toString('base64');
            
            // Upload to Blogger (this would need to be implemented in bloggerService)
            const bloggerImageUrl = await bloggerService.uploadImage(blogId, {
                data: base64Image,
                mimeType: imageData.mimeType,
                filename: imageData.originalName
            });

            return bloggerImageUrl;
        } catch (error) {
            console.error('Failed to upload image to Blogger:', error);
            throw error;
        }
    }

    validateImageData(imageData) {
        const required = ['filename', 'mimeType', 'altText'];
        const missing = required.filter(field => !imageData[field]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required image fields: ${missing.join(', ')}`);
        }

        if (!this.allowedMimeTypes.includes(imageData.mimeType)) {
            throw new Error(`Invalid image type: ${imageData.mimeType}`);
        }

        return true;
    }

    getImageInfo(filename) {
        return {
            url: `/uploads/${filename}`,
            path: path.join(this.uploadDir, filename)
        };
    }

    async uploadToPublicHost(imagePath, filename) {
        try {
            // Check if we have a public hosting service configured
            if (process.env.IMGBB_API_KEY) {
                // Use ImgBB for free image hosting
                return await this.uploadToImgBB(imagePath, filename);
            } else if (process.env.NODE_ENV === 'production' && process.env.BASE_URL && !process.env.BASE_URL.includes('localhost')) {
                // Use production server URL
                const baseUrl = process.env.BASE_URL;
                const publicUrl = `${baseUrl}/uploads/${filename}`;

                console.log('Using production server for image hosting:', {
                    filename,
                    publicUrl
                });

                return publicUrl;
            } else {
                // Development mode - use localhost (will only work locally)
                const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
                const publicUrl = `${baseUrl}/uploads/${filename}`;

                console.warn('Using localhost for image hosting - images will not be accessible from external services like Blogger in production!');
                console.log('For production, consider setting up:', {
                    filename,
                    publicUrl,
                    recommendations: [
                        'Set IMGBB_API_KEY for free image hosting',
                        'Deploy to a public server and set BASE_URL',
                        'Use AWS S3, Cloudinary, or similar service'
                    ]
                });

                return publicUrl;
            }
        } catch (error) {
            console.error('Failed to upload image to public host:', error);
            throw error;
        }
    }

    async uploadToImgBB(imagePath, filename) {
        try {
            // Read image file and convert to base64
            const imageBuffer = await fs.readFile(imagePath);
            const base64Image = imageBuffer.toString('base64');

            // Upload to ImgBB
            const response = await axios.post('https://api.imgbb.com/1/upload', {
                key: process.env.IMGBB_API_KEY,
                image: base64Image,
                name: filename
            }, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (response.data && response.data.success) {
                const publicUrl = response.data.data.url;
                console.log('Image uploaded to ImgBB successfully:', {
                    filename,
                    publicUrl
                });
                return publicUrl;
            } else {
                throw new Error('ImgBB upload failed');
            }
        } catch (error) {
            console.error('ImgBB upload failed:', error);
            // Fallback to local hosting
            const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
            return `${baseUrl}/uploads/${filename}`;
        }
    }

    async uploadBufferToImgBB(buffer, filename) {
        try {
            // Convert buffer to base64
            const base64Image = buffer.toString('base64');

            // Upload to ImgBB
            const response = await axios.post('https://api.imgbb.com/1/upload', {
                key: process.env.IMGBB_API_KEY,
                image: base64Image,
                name: filename
            }, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (response.data && response.data.success) {
                const publicUrl = response.data.data.url;
                console.log('Image uploaded to ImgBB successfully from buffer:', {
                    filename,
                    publicUrl,
                    size: buffer.length
                });
                return publicUrl;
            } else {
                throw new Error('ImgBB upload failed');
            }
        } catch (error) {
            console.error('ImgBB upload from buffer failed:', error);
            throw error;
        }
    }

    async uploadToCloudStorage(imagePath, filename) {
        // Placeholder for cloud storage implementation
        // This would integrate with services like:
        // - AWS S3
        // - Cloudinary
        // - ImgBB
        // - Google Cloud Storage
        // - etc.

        console.log('Cloud storage upload not implemented yet');
        throw new Error('Cloud storage upload not implemented');
    }

    async processImageForPublishing(imageData) {
        try {
            // If image already has a public URL (uploaded directly to ImgBB), use it
            if (imageData.publicUrl) {
                console.log('Image already has public URL:', {
                    filename: imageData.filename,
                    publicUrl: imageData.publicUrl
                });
                return imageData;
            }

            // Fallback: if image was stored locally, upload it now
            if (imageData.path) {
                const publicUrl = await this.uploadToPublicHost(imageData.path, imageData.filename);

                const updatedImageData = {
                    ...imageData,
                    publicUrl: publicUrl,
                    originalUrl: imageData.url
                };

                console.log('Image processed for publishing:', {
                    filename: imageData.filename,
                    originalUrl: imageData.url,
                    publicUrl: publicUrl
                });

                return updatedImageData;
            }

            // If no path and no public URL, return as-is (shouldn't happen with new implementation)
            console.warn('Image has no path or public URL:', imageData);
            return imageData;
        } catch (error) {
            console.error('Failed to process image for publishing:', error);
            throw error;
        }
    }
}

module.exports = new ImageService();
