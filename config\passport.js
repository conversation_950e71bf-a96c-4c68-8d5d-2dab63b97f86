const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const config = require('./config');

// Configure Google OAuth strategy
console.log('OAuth Configuration:', {
  clientId: config.google.clientId ? 'SET' : 'MISSING',
  clientSecret: config.google.clientSecret ? 'SET' : 'MISSING',
  redirectUri: config.google.redirectUri,
  nodeEnv: config.nodeEnv
});

passport.use(new GoogleStrategy({
  clientID: config.google.clientId,
  clientSecret: config.google.clientSecret,
  callbackURL: config.google.redirectUri
}, async (accessToken, refreshToken, profile, done) => {
  try {
    // Create user object with necessary information
    const user = {
      id: profile.id,
      displayName: profile.displayName,
      emails: profile.emails,
      photos: profile.photos,
      accessToken: accessToken,
      refreshToken: refreshToken,
      provider: 'google'
    };
    
    return done(null, user);
  } catch (error) {
    return done(error, null);
  }
}));

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user);
});

// Deserialize user from session
passport.deserializeUser((user, done) => {
  done(null, user);
});

module.exports = passport;
