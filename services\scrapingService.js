const cheerio = require('cheerio');
const axios = require('axios');
const puppeteer = require('puppeteer');
const config = require('../config/config');

class ScrapingService {
  constructor() {
    this.timeout = config.scrapingTimeout;
    this.maxContentLength = config.maxContentLength;
  }

  async scrapeArticle(url, articleType = 'general') {
    try {
      // Validate URL
      if (!this.isValidUrl(url)) {
        throw new Error('Invalid URL provided');
      }

      // Check if this URL likely has cookie popups and needs browser-based scraping
      if (this.needsBrowserScraping(url)) {
        console.log('Using browser-based scraping for:', url);
        return await this.scrapeWithBrowser(url, articleType);
      }

      // Use traditional axios scraping for simpler sites
      console.log('Using traditional scraping for:', url);
      return await this.scrapeWithAxios(url, articleType);

    } catch (error) {
      console.error('Scraping error:', error.message);

      // If traditional scraping fails, try browser-based scraping as fallback
      if (!this.needsBrowserScraping(url)) {
        console.log('Traditional scraping failed, trying browser-based scraping...');
        try {
          return await this.scrapeWithBrowser(url, articleType);
        } catch (browserError) {
          console.error('Browser scraping also failed:', browserError.message);
        }
      }

      throw new Error(`Failed to scrape article: ${error.message}`);
    }
  }

  /**
   * Check if URL needs browser-based scraping (for cookie popups, etc.)
   */
  needsBrowserScraping(url) {
    const browserScrapingDomains = [
      'unstop.com',
      'medium.com',
      'linkedin.com',
      'facebook.com',
      'twitter.com',
      'instagram.com',
      'tiktok.com',
      'reddit.com',
      'in.indeed.com'
    ];

    return browserScrapingDomains.some(domain => url.includes(domain));
  }

  /**
   * Traditional axios-based scraping
   */
  async scrapeWithAxios(url, articleType) {
    // Configure axios with timeout and headers
    const response = await axios.get(url, {
      timeout: this.timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    });

    const $ = cheerio.load(response.data);

    // Extract content based on article type
    const extractedData = this.extractContent($, articleType, url);

    // Validate content length
    if (extractedData.content.length > this.maxContentLength) {
      extractedData.content = extractedData.content.substring(0, this.maxContentLength) + '...';
      extractedData.truncated = true;
    }

    return {
      success: true,
      url: url,
      articleType: articleType,
      ...extractedData,
      scrapedAt: new Date().toISOString()
    };
  }

  /**
   * Browser-based scraping using Puppeteer (handles cookie popups)
   */
  async scrapeWithBrowser(url, articleType) {
    let browser = null;
    let page = null;

    try {
      // Launch browser with enhanced options for cookie handling
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-background-networking',
          '--disable-background-timer-throttling',
          '--disable-renderer-backgrounding',
          '--disable-backgrounding-occluded-windows'
        ]
      });

      page = await browser.newPage();

      // Enhanced bot detection avoidance
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

      // Set additional headers to appear more like a real browser
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none'
      });

      await page.setViewport({ width: 1366, height: 768 });

      // Enable JavaScript and cookies
      await page.setJavaScriptEnabled(true);

      // Navigate to the page
      console.log('Navigating to:', url);
      await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: this.timeout
      });

      // Wait for initial page load
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Handle cookie popups (enhanced)
      await this.handleCookiePopups(page);

      // Wait for any dynamic content to load after cookie handling
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Try to scroll down to trigger any lazy loading
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight / 2);
      });
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Scroll back to top
      await page.evaluate(() => {
        window.scrollTo(0, 0);
      });
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get page content
      const content = await page.content();
      const $ = cheerio.load(content);

      // Extract content based on article type
      const extractedData = this.extractContent($, articleType, url);

      // Validate content length
      if (extractedData.content.length > this.maxContentLength) {
        extractedData.content = extractedData.content.substring(0, this.maxContentLength) + '...';
        extractedData.truncated = true;
      }

      return {
        success: true,
        url: url,
        articleType: articleType,
        ...extractedData,
        scrapedAt: new Date().toISOString(),
        scrapingMethod: 'browser'
      };

    } finally {
      if (page) await page.close();
      if (browser) await browser.close();
    }
  }

  /**
   * Handle various cookie popup patterns with enhanced detection
   */
  async handleCookiePopups(page) {
    console.log('Starting enhanced cookie popup handling...');

    // Step 1: Try multiple rounds of cookie popup detection
    for (let attempt = 1; attempt <= 3; attempt++) {
      console.log(`Cookie popup detection attempt ${attempt}/3`);

      const found = await this.attemptCookiePopupDismissal(page);
      if (found) {
        console.log(`Cookie popup dismissed on attempt ${attempt}`);
        break;
      }

      // Wait between attempts
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Step 2: Aggressive overlay removal
    await this.removeOverlaysAndPopups(page);

    // Step 3: Enable cookies programmatically
    await this.enableCookiesProgrammatically(page);

    console.log('Cookie popup handling completed');
  }

  /**
   * Attempt to find and dismiss cookie popups
   */
  async attemptCookiePopupDismissal(page) {
    const cookieSelectors = [
      // Enhanced cookie popup selectors
      '[data-testid="cookie-banner"] button',
      '[data-testid="accept-cookies"]',
      '[data-testid="cookie-accept"]',
      '[data-cy="accept-cookies"]',
      '.cookie-banner button',
      '.cookie-consent button',
      '.cookie-notice button',
      '.cookie-popup button',
      '.cookie-bar button',
      '.cookie-alert button',
      '#cookie-banner button',
      '#cookie-consent button',
      '#cookie-notice button',
      '#cookie-popup button',
      '.gdpr-banner button',
      '.privacy-banner button',
      '.consent-banner button',
      '.privacy-notice button',
      'button[id*="cookie"]',
      'button[class*="cookie"]',
      'button[class*="accept"]',
      'button[class*="consent"]',
      'button[class*="agree"]',
      'button[data-cy*="cookie"]',
      'button[data-testid*="cookie"]',
      // Text-based selectors (more flexible)
      'button:contains("Accept")',
      'button:contains("Accept All")',
      'button:contains("Accept Cookies")',
      'button:contains("I Accept")',
      'button:contains("Agree")',
      'button:contains("Agree All")',
      'button:contains("OK")',
      'button:contains("Got it")',
      'button:contains("Continue")',
      'button:contains("Allow")',
      'button:contains("Allow All")',
      'button:contains("Enable")',
      'button:contains("Yes")',
      'a:contains("Accept")',
      'a:contains("Accept All")',
      'a:contains("I Accept")',
      'a:contains("Agree")',
      'a:contains("Continue")',
      // Modal and popup specific
      '.modal button:contains("Accept")',
      '.popup button:contains("Accept")',
      '.dialog button:contains("Accept")',
      '.overlay button:contains("Accept")',
      // Common class patterns
      '.btn-accept',
      '.accept-btn',
      '.cookie-accept',
      '.consent-accept',
      '.privacy-accept'
    ];

    for (const selector of cookieSelectors) {
      try {
        // Check if element exists
        const element = await page.$(selector);
        if (element) {
          // Check if element is visible
          const isVisible = await page.evaluate(el => {
            const style = window.getComputedStyle(el);
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
          }, element);

          if (isVisible) {
            await page.click(selector);
            console.log('Successfully clicked cookie popup button:', selector);

            // Wait for popup to disappear
            await new Promise(resolve => setTimeout(resolve, 1500));
            return true;
          }
        }
      } catch (error) {
        // Continue to next selector
        continue;
      }
    }

    return false;
  }

  /**
   * Aggressively remove overlays and popups
   */
  async removeOverlaysAndPopups(page) {
    try {
      await page.evaluate(() => {
        // Remove common overlay classes
        const overlaySelectors = [
          '.modal-backdrop',
          '.overlay',
          '.popup-overlay',
          '.cookie-overlay',
          '.consent-overlay',
          '.privacy-overlay',
          '.modal-overlay',
          '.backdrop',
          '.mask',
          '.screen',
          '.blocker'
        ];

        overlaySelectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach(el => el.remove());
        });

        // Remove elements with cookie-related content and high z-index
        const allElements = Array.from(document.querySelectorAll('*'));
        allElements.forEach(el => {
          const style = window.getComputedStyle(el);
          const zIndex = parseInt(style.zIndex);

          if (zIndex > 1000) {
            const text = el.textContent.toLowerCase();
            const cookieKeywords = [
              'cookie', 'consent', 'privacy', 'gdpr', 'tracking',
              'accept', 'agree', 'enable', 'allow', 'continue'
            ];

            if (cookieKeywords.some(keyword => text.includes(keyword))) {
              el.style.display = 'none';
              el.remove();
            }
          }
        });

        // Remove fixed positioned elements that might be popups
        const fixedElements = Array.from(document.querySelectorAll('*')).filter(el => {
          const style = window.getComputedStyle(el);
          return style.position === 'fixed' && parseInt(style.zIndex) > 100;
        });

        fixedElements.forEach(el => {
          const text = el.textContent.toLowerCase();
          if (text.includes('cookie') || text.includes('consent') || text.includes('privacy')) {
            el.style.display = 'none';
            el.remove();
          }
        });
      });

      console.log('Removed overlays and popups');
    } catch (error) {
      console.log('Could not remove overlays:', error.message);
    }
  }

  /**
   * Enable cookies programmatically
   */
  async enableCookiesProgrammatically(page) {
    try {
      await page.evaluate(() => {
        // Set common cookie consent flags
        localStorage.setItem('cookieConsent', 'true');
        localStorage.setItem('cookie-consent', 'true');
        localStorage.setItem('cookies-accepted', 'true');
        localStorage.setItem('gdpr-consent', 'true');
        localStorage.setItem('privacy-consent', 'true');

        // Set cookies
        document.cookie = 'cookieConsent=true; path=/; max-age=31536000';
        document.cookie = 'cookie-consent=true; path=/; max-age=31536000';
        document.cookie = 'cookies-accepted=true; path=/; max-age=31536000';
        document.cookie = 'gdpr-consent=true; path=/; max-age=31536000';

        // Try to trigger any consent acceptance functions
        if (window.cookieConsent && typeof window.cookieConsent.accept === 'function') {
          window.cookieConsent.accept();
        }

        if (window.gtag) {
          window.gtag('consent', 'update', {
            'analytics_storage': 'granted',
            'ad_storage': 'granted'
          });
        }
      });

      console.log('Set cookie consent programmatically');
    } catch (error) {
      console.log('Could not set cookie consent:', error.message);
    }
  }

  extractContent($, articleType, url) {
    let title = '';
    let content = '';
    let description = '';
    let author = '';
    let publishDate = '';
    let tags = [];
    let featuredImage = '';

    // Extract title
    title = this.extractTitle($);
    
    // Extract meta description
    description = this.extractMetaDescription($);
    
    // Extract author
    author = this.extractAuthor($);
    
    // Extract publish date
    publishDate = this.extractPublishDate($);
    
    // Extract featured image
    featuredImage = this.extractFeaturedImage($, url);
    
    // Extract tags/keywords
    tags = this.extractTags($);
    
    // Extract main content based on article type
    content = this.extractMainContent($, articleType);

    return {
      title: title.trim(),
      content: content.trim(),
      description: description.trim(),
      author: author.trim(),
      publishDate: publishDate.trim(),
      tags: tags,
      featuredImage: featuredImage,
      wordCount: content.split(/\s+/).length
    };
  }

  extractTitle($) {
    return $('title').text() ||
           $('h1').first().text() ||
           $('meta[property="og:title"]').attr('content') ||
           $('meta[name="twitter:title"]').attr('content') ||
           '';
  }

  extractMetaDescription($) {
    return $('meta[name="description"]').attr('content') ||
           $('meta[property="og:description"]').attr('content') ||
           $('meta[name="twitter:description"]').attr('content') ||
           '';
  }

  extractAuthor($) {
    return $('meta[name="author"]').attr('content') ||
           $('meta[property="article:author"]').attr('content') ||
           $('.author').first().text() ||
           $('[rel="author"]').first().text() ||
           '';
  }

  extractPublishDate($) {
    return $('meta[property="article:published_time"]').attr('content') ||
           $('meta[name="publish_date"]').attr('content') ||
           $('time[datetime]').attr('datetime') ||
           $('.date').first().text() ||
           '';
  }

  extractFeaturedImage($, baseUrl) {
    let imageUrl = $('meta[property="og:image"]').attr('content') ||
                   $('meta[name="twitter:image"]').attr('content') ||
                   $('img').first().attr('src') ||
                   '';
    
    // Convert relative URLs to absolute
    if (imageUrl && !imageUrl.startsWith('http')) {
      try {
        const base = new URL(baseUrl);
        imageUrl = new URL(imageUrl, base.origin).href;
      } catch (e) {
        imageUrl = '';
      }
    }
    
    return imageUrl;
  }

  extractTags($) {
    const tags = [];
    
    // Extract from meta keywords
    const keywords = $('meta[name="keywords"]').attr('content');
    if (keywords) {
      tags.push(...keywords.split(',').map(tag => tag.trim()));
    }
    
    // Extract from article tags
    $('.tag, .tags a, .category, .categories a').each((i, el) => {
      const tag = $(el).text().trim();
      if (tag && !tags.includes(tag)) {
        tags.push(tag);
      }
    });
    
    return tags.slice(0, 10); // Limit to 10 tags
  }

  extractMainContent($, articleType) {
    // Common content selectors
    const contentSelectors = [
      'article',
      '.article-content',
      '.post-content',
      '.entry-content',
      '.content',
      '.main-content',
      '#content',
      '.article-body',
      '.post-body'
    ];

    let content = '';
    
    // Try each selector until we find content
    for (const selector of contentSelectors) {
      const element = $(selector);
      if (element.length && element.text().trim().length > 100) {
        content = this.cleanContent(element.html());
        break;
      }
    }
    
    // Fallback: extract from paragraphs
    if (!content) {
      const paragraphs = $('p').map((i, el) => $(el).text().trim()).get();
      content = paragraphs.filter(p => p.length > 20).join('\n\n');
    }
    
    return content;
  }

  cleanContent(html) {
    if (!html) return '';
    
    const $ = cheerio.load(html);
    
    // Remove unwanted elements
    $('script, style, nav, header, footer, aside, .advertisement, .ads, .social-share').remove();
    
    // Clean up the text
    return $.text()
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim();
  }

  isValidUrl(string) {
    try {
      const url = new URL(string);
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (_) {
      return false;
    }
  }
}

module.exports = new ScrapingService();
