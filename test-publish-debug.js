/**
 * Debug script to test publishing functionality
 * Run this to check if the publishing service works correctly
 */

const config = require('./config/config');
const bloggerService = require('./services/bloggerService');

// Mock user object (replace with real data for testing)
const mockUser = {
  id: 'test-user-id',
  displayName: 'Test User',
  emails: [{ value: '<EMAIL>' }],
  accessToken: 'your-access-token-here', // Replace with real token
  refreshToken: 'your-refresh-token-here', // Replace with real token
  provider: 'google'
};

// Mock post data
const mockPostData = {
  blogId: 'your-blog-id-here', // Replace with real blog ID
  title: 'Test Post - Debug Publishing',
  content: '<p>This is a test post to debug the publishing functionality.</p><p>If you see this, the publishing is working correctly.</p>',
  labels: ['test', 'debug'],
  metaDescription: 'Test post for debugging publishing functionality',
  isDraft: true // Set to true for safety
};

async function testPublishing() {
  try {
    console.log('🔧 Testing publishing functionality...');
    console.log('Configuration check:', {
      hasClientId: !!config.google.clientId,
      hasClientSecret: !!config.google.clientSecret,
      redirectUri: config.google.redirectUri,
      nodeEnv: config.nodeEnv
    });

    console.log('\n📝 Mock user data:', {
      userId: mockUser.id,
      hasAccessToken: !!mockUser.accessToken,
      hasRefreshToken: !!mockUser.refreshToken
    });

    console.log('\n📄 Mock post data:', {
      blogId: mockPostData.blogId,
      titleLength: mockPostData.title.length,
      contentLength: mockPostData.content.length,
      isDraft: mockPostData.isDraft
    });

    console.log('\n🚀 Attempting to publish...');
    const result = await bloggerService.publishPost(mockUser, mockPostData);
    
    console.log('\n✅ Publishing successful!');
    console.log('Result:', {
      postId: result.id,
      title: result.title,
      url: result.url,
      status: result.status
    });

  } catch (error) {
    console.error('\n❌ Publishing failed:');
    console.error('Error message:', error.message);
    console.error('Error code:', error.code);
    console.error('Full error:', error);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  console.log('⚠️  WARNING: This is a debug script. Make sure to:');
  console.log('1. Replace mock data with real values');
  console.log('2. Set isDraft: true for safety');
  console.log('3. Remove this file after debugging\n');
  
  testPublishing();
}

module.exports = { testPublishing };
