<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - Article Rewriter</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .ad-unit-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .ad-unit-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .ad-unit-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
        }
        .ad-unit-content {
            padding: 15px;
        }
        .code-editor {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            min-height: 150px;
        }
        .settings-section {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .preview-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-edit me-2"></i>Article Rewriter
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>Home
                </a>
                <% if (user) { %>
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i><%= user.emails[0].value %>
                    </span>
                    <a class="nav-link" href="/auth/logout">
                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                    </a>
                <% } else { %>
                    <a class="nav-link" href="/auth/blogger">
                        <i class="fab fa-blogger me-1"></i>Login with Google
                    </a>
                <% } %>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-ad me-2"></i>AdSense Settings</h1>
                    <div>
                        <button type="button" class="btn btn-outline-primary me-2" id="testAdInsertionBtn">
                            <i class="fas fa-vial me-1"></i>Test Ad Insertion
                        </button>
                        <button type="button" class="btn btn-success" id="saveSettingsBtn">
                            <i class="fas fa-save me-1"></i>Save Settings
                        </button>
                    </div>
                </div>

                <!-- Alert container -->
                <div id="alertContainer"></div>

                <!-- General Settings -->
                <div class="settings-section">
                    <h3><i class="fas fa-cog me-2"></i>General Settings</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="autoInsertAds" checked>
                                <label class="form-check-label" for="autoInsertAds">
                                    <strong>Enable Automatic Ad Insertion</strong>
                                    <small class="d-block text-muted">Automatically insert ads when publishing articles</small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="adsPerArticle" class="form-label">
                                    <strong>Ads per Article</strong>
                                </label>
                                <select class="form-select" id="adsPerArticle">
                                    <option value="1">1 Ad</option>
                                    <option value="2">2 Ads</option>
                                    <option value="3" selected>3 Ads</option>
                                    <option value="4">4 Ads</option>
                                </select>
                                <small class="form-text text-muted">Number of ads to insert per article</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="minParagraphsBetweenAds" class="form-label">
                                    <strong>Min Paragraphs Between Ads</strong>
                                </label>
                                <input type="number" class="form-control" id="minParagraphsBetweenAds" value="2" min="1" max="5">
                                <small class="form-text text-muted">Minimum spacing between ads</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="skipFirstParagraphs" class="form-label">
                                    <strong>Skip First Paragraphs</strong>
                                </label>
                                <input type="number" class="form-control" id="skipFirstParagraphs" value="1" min="0" max="3">
                                <small class="form-text text-muted">Don't place ads in first N paragraphs</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="skipLastParagraphs" class="form-label">
                                    <strong>Skip Last Paragraphs</strong>
                                </label>
                                <input type="number" class="form-control" id="skipLastParagraphs" value="1" min="0" max="3">
                                <small class="form-text text-muted">Don't place ads in last N paragraphs</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ad Units Configuration -->
                <div class="settings-section">
                    <h3><i class="fas fa-code me-2"></i>AdSense Ad Units</h3>
                    <p class="text-muted mb-4">Configure your Google AdSense ad unit scripts. These will be rotated and inserted into your articles.</p>
                    
                    <div id="adUnitsContainer">
                        <!-- Ad units will be loaded here -->
                    </div>
                    
                    <button type="button" class="btn btn-outline-primary" id="addAdUnitBtn">
                        <i class="fas fa-plus me-1"></i>Add New Ad Unit
                    </button>
                </div>

                <!-- Test Section -->
                <div class="settings-section" id="testSection" style="display: none;">
                    <h3><i class="fas fa-vial me-2"></i>Test Ad Insertion</h3>
                    <div class="mb-3">
                        <label for="testContent" class="form-label">
                            <strong>Test Content (HTML)</strong>
                        </label>
                        <textarea class="form-control" id="testContent" rows="8" placeholder="Paste your article content here to test ad insertion..."></textarea>
                    </div>
                    <button type="button" class="btn btn-primary" id="runTestBtn">
                        <i class="fas fa-play me-1"></i>Run Test
                    </button>
                    <div id="testResults" class="preview-container" style="display: none;">
                        <h5>Test Results:</h5>
                        <div id="testOutput"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ad Unit Template -->
    <template id="adUnitTemplate">
        <div class="ad-unit-card" data-unit-id="">
            <div class="ad-unit-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">
                            <input type="text" class="form-control form-control-sm d-inline-block" style="width: 200px;" placeholder="Ad Unit Name" data-field="name">
                        </h5>
                        <small class="text-muted">Ad Unit ID: <span data-field="id"></span></small>
                    </div>
                    <div>
                        <div class="form-check form-switch me-3">
                            <input class="form-check-input" type="checkbox" data-field="enabled" checked>
                            <label class="form-check-label">Enabled</label>
                        </div>
                        <button type="button" class="btn btn-outline-danger btn-sm delete-ad-unit">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="ad-unit-content">
                <label class="form-label"><strong>AdSense Script Code:</strong></label>
                <textarea class="form-control code-editor" data-field="script" rows="8" placeholder="Paste your AdSense ad unit script here..."></textarea>
                <small class="form-text text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Copy the complete ad unit code from your Google AdSense dashboard
                </small>
            </div>
        </div>
    </template>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/adsense-settings.js"></script>
</body>
</html>
