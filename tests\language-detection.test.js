// Mock the Gemini API for testing
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
    getGenerativeModel: jest.fn().mockReturnValue({
      generateContent: jest.fn().mockImplementation((prompt) => {
        // Mock language detection responses
        if (prompt.includes('Language:')) {
          if (prompt.includes('नमस्ते')) {
            return Promise.resolve({
              response: { text: () => 'Hindi' }
            });
          } else if (prompt.includes('नमस्कार')) {
            return Promise.resolve({
              response: { text: () => 'Marathi' }
            });
          } else {
            return Promise.resolve({
              response: { text: () => 'English' }
            });
          }
        }

        // Mock content rewriting responses
        return Promise.resolve({
          response: {
            text: () => JSON.stringify({
              title: 'Test Article',
              content: '<h1>Test Content</h1><p>This is test content.</p>',
              metaDescription: 'Test meta description',
              tags: ['test', 'article'],
              permalink: 'test-article',
              summary: 'Test summary'
            })
          }
        });
      })
    })
  }))
}));

// Mock the config
jest.mock('../config/config', () => ({
  geminiApiKey: 'test-api-key'
}));

const geminiService = require('../services/geminiService');

describe('Language Detection and Preservation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Language Detection', () => {
    test('should detect Hindi language', async () => {
      const hindiText = 'नमस्ते, यह एक हिंदी लेख है।';
      const language = await geminiService.detectLanguage(hindiText);
      expect(language).toBe('Hindi');
    });

    test('should detect Marathi language', async () => {
      const marathiText = 'नमस्कार, हा एक मराठी लेख आहे।';
      const language = await geminiService.detectLanguage(marathiText);
      expect(language).toBe('Marathi');
    });

    test('should detect English language', async () => {
      const englishText = 'Hello, this is an English article.';
      const language = await geminiService.detectLanguage(englishText);
      expect(language).toBe('English');
    });

    test('should default to English on detection failure', async () => {
      // Mock a failure scenario by temporarily replacing the method
      const originalMethod = geminiService.model.generateContent;
      geminiService.model.generateContent = jest.fn().mockRejectedValue(new Error('API Error'));

      const language = await geminiService.detectLanguage('Some text');
      expect(language).toBe('English');

      // Restore original method
      geminiService.model.generateContent = originalMethod;
    });
  });

  describe('Language Normalization', () => {
    test('should normalize language names correctly', () => {
      expect(geminiService.normalizeLanguage('hindi')).toBe('Hindi');
      expect(geminiService.normalizeLanguage('MARATHI')).toBe('Marathi');
      expect(geminiService.normalizeLanguage('english')).toBe('English');
      expect(geminiService.normalizeLanguage('Unknown')).toBe('Unknown');
      expect(geminiService.normalizeLanguage('')).toBe('English');
    });
  });

  describe('Content Rewriting with Language Preservation', () => {
    test('should include language information in rewritten content', async () => {
      const content = 'Test article content';
      const title = 'Test Title';
      
      const result = await geminiService.rewriteContent({
        content,
        title,
        articleType: 'general'
      });

      expect(result.success).toBe(true);
      expect(result.detectedLanguage).toBeDefined();
      expect(result.title).toBeDefined();
      expect(result.content).toBeDefined();
      expect(result.rewrittenAt).toBeDefined();
    });

    test('should build prompt with language instructions for non-English content', () => {
      const promptData = {
        content: 'Test content',
        title: 'Test Title',
        articleType: 'general',
        language: 'Hindi'
      };

      const prompt = geminiService.buildPrompt(promptData);
      
      expect(prompt).toContain('CONTENT LANGUAGE: Hindi');
      expect(prompt).toContain('You MUST rewrite the content in the SAME language (Hindi)');
      expect(prompt).toContain('Write ALL content in Hindi');
    });

    test('should build prompt without special language instructions for English', () => {
      const promptData = {
        content: 'Test content',
        title: 'Test Title',
        articleType: 'general',
        language: 'English'
      };

      const prompt = geminiService.buildPrompt(promptData);
      
      expect(prompt).toContain('CONTENT LANGUAGE: English');
      expect(prompt).not.toContain('You MUST rewrite the content in the SAME language');
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      // Mock API failure
      const originalMethod = geminiService.model.generateContent;
      geminiService.model.generateContent = jest.fn().mockRejectedValue(new Error('API quota exceeded'));

      await expect(geminiService.rewriteContent({
        content: 'Test content',
        title: 'Test Title'
      })).rejects.toThrow('API quota exceeded. Please try again later.');

      // Restore original method
      geminiService.model.generateContent = originalMethod;
    });

    test('should handle safety filter errors', async () => {
      // Mock safety filter error
      const originalMethod = geminiService.model.generateContent;
      geminiService.model.generateContent = jest.fn().mockRejectedValue(new Error('safety filter triggered'));

      await expect(geminiService.rewriteContent({
        content: 'Test content',
        title: 'Test Title'
      })).rejects.toThrow('Content was flagged by safety filters. Please try different content.');

      // Restore original method
      geminiService.model.generateContent = originalMethod;
    });
  });
});
