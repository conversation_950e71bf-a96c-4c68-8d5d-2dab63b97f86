# Article Rewriter App

A comprehensive Node.js web application for scraping, rewriting, and publishing articles using AI-powered content generation and Blogger API integration.

## Features

- **Article Scraping**: Extract content from any web URL with intelligent content detection
- **AI-Powered Rewriting**: Use Google's Gemini AI to rewrite content for SEO optimization and plagiarism-free results
- **Language Detection & Preservation**: Automatically detects source language (Hindi, Marathi, English, etc.) and preserves it during rewriting
- **Blogger Integration**: Publish directly to your Blogger account with Google OAuth authentication
- **Content Management**: Edit and preview content before publishing
- **Security**: Built-in rate limiting, input validation, and XSS protection
- **Responsive UI**: Modern, mobile-friendly interface

## Prerequisites

- Node.js (v20.17.0 or higher)
- npm (v11.4.2 or higher)
- Google Cloud Project with Blogger API enabled
- Gemini AI API key

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd article-rewriter-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   Copy the provided `.env` file or create one with the following variables:
   ```env
   PORT=3000
   NODE_ENV=development
   GEMINI_API_KEY=your_gemini_api_key
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100
   SCRAPING_TIMEOUT=30000
   MAX_CONTENT_LENGTH=50000
   SESSION_SECRET=your_session_secret
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   BLOGGER_REDIRECT_URI=http://localhost:3000/auth/blogger/callback
   CORS_ORIGIN=http://localhost:3000
   ```

4. **Start the application**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

5. **Access the application**
   Open your browser and navigate to `http://localhost:3000`

## Usage

### 1. Authentication
- Click "Login with Google" to authenticate with your Google account
- Grant permissions for Blogger access

### 2. Article Processing
1. **Enter URL**: Paste the URL of the article you want to rewrite
2. **Select Type**: Choose the appropriate article type (news, blog, job posting, etc.)
3. **Add Instructions**: Optionally provide custom instructions for the AI
4. **Scrape Content**: Click "Scrape Content" to extract the article
5. **Rewrite**: Click "Rewrite with AI" to generate plagiarism-free content
6. **Edit**: Review and edit the rewritten content as needed
7. **Publish**: Select your blog and publish the article

### 3. Configuration
- Access settings via the gear icon in the navigation
- Update your Gemini API key
- Configure processing options

## API Endpoints

### Articles
- `POST /api/articles/validate-url` - Validate article URL
- `POST /api/articles/scrape` - Scrape article content
- `POST /api/articles/rewrite` - Rewrite content with AI
- `GET /api/articles/blogs` - Get user's Blogger blogs (requires auth)
- `POST /api/articles/publish` - Publish to Blogger (requires auth)
- `POST /api/articles/process` - Complete pipeline (scrape + rewrite + publish)

### Authentication
- `GET /auth/google` - Initiate Google OAuth
- `GET /auth/blogger/callback` - OAuth callback
- `GET /auth/logout` - Logout user
- `GET /auth/status` - Check authentication status

### Configuration
- `GET /api/config` - Get current configuration
- `POST /api/config/gemini-key` - Update Gemini API key

### Health Check
- `GET /health` - Application health status

## Security Features

- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Input Validation**: Comprehensive validation for all inputs
- **Content Sanitization**: XSS protection and malicious content removal
- **CSRF Protection**: Session-based CSRF protection
- **Secure Headers**: Helmet.js for security headers
- **Content Length Limits**: Prevents oversized content attacks

## Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment mode | development |
| `GEMINI_API_KEY` | Google Gemini AI API key | Required |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window in milliseconds | 900000 (15 min) |
| `RATE_LIMIT_MAX_REQUESTS` | Max requests per window | 100 |
| `SCRAPING_TIMEOUT` | Scraping timeout in milliseconds | 30000 (30 sec) |
| `MAX_CONTENT_LENGTH` | Maximum content length | 50000 |
| `SESSION_SECRET` | Session encryption secret | Required |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | Required |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | Required |
| `BLOGGER_REDIRECT_URI` | OAuth redirect URI | Required |
| `CORS_ORIGIN` | CORS allowed origin | http://localhost:3000 |

## Testing

Run the test suite:
```bash
npm test
```

The tests cover:
- API endpoints
- Authentication flows
- Input validation
- Rate limiting
- Security measures
- Error handling

## Project Structure

```
article-rewriter-app/
├── config/
│   ├── config.js          # Application configuration
│   └── passport.js        # Passport OAuth configuration
├── middleware/
│   ├── errorHandler.js    # Error handling middleware
│   ├── rateLimiter.js     # Rate limiting middleware
│   └── validation.js      # Input validation middleware
├── routes/
│   ├── articles.js        # Article processing routes
│   ├── auth.js           # Authentication routes
│   └── config.js         # Configuration routes
├── services/
│   ├── articleService.js  # Main article processing service
│   ├── bloggerService.js  # Blogger API integration
│   ├── geminiService.js   # Gemini AI integration
│   └── scrapingService.js # Web scraping service
├── public/
│   ├── css/
│   ├── js/
│   └── images/
├── views/
│   ├── index.ejs         # Main application page
│   └── error.ejs         # Error page template
├── tests/
│   └── app.test.js       # Test suite
├── .env                  # Environment variables
├── server.js             # Main server file
└── package.json          # Dependencies and scripts
```

## Troubleshooting

### Common Issues

1. **Gemini API Errors**
   - Verify your API key is correct
   - Check API quota limits
   - Ensure content doesn't violate safety policies

2. **Scraping Failures**
   - Some websites block automated requests
   - Check if the URL is accessible
   - Verify the website structure hasn't changed

3. **Blogger Publishing Issues**
   - Ensure you're authenticated with Google
   - Check blog permissions
   - Verify the blog ID is correct

4. **Rate Limiting**
   - Wait for the rate limit window to reset
   - Consider upgrading API limits for production use

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For support and questions, please open an issue in the repository.
