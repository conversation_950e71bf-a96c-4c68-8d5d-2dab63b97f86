/**
 * Test script to verify label validation functionality
 */

const bloggerService = require('./services/bloggerService');

// Test cases for label validation
const testCases = [
  {
    name: 'Normal labels (should pass)',
    labels: ['tech', 'programming', 'javascript', 'web development'],
    expectedPass: true
  },
  {
    name: 'Too many long labels (should truncate)',
    labels: [
      'very-long-technology-label-that-exceeds-normal-length',
      'another-extremely-long-programming-label-for-testing',
      'javascript-frameworks-and-libraries-comprehensive-guide',
      'web-development-best-practices-and-modern-techniques',
      'software-engineering-principles-and-design-patterns',
      'database-management-and-optimization-strategies',
      'cloud-computing-and-microservices-architecture',
      'artificial-intelligence-and-machine-learning-basics'
    ],
    expectedPass: true // Should truncate to fit
  },
  {
    name: 'Empty labels',
    labels: [],
    expectedPass: true
  },
  {
    name: 'Labels with whitespace',
    labels: ['  tech  ', '', '   programming   ', 'javascript'],
    expectedPass: true
  },
  {
    name: 'Single very long label',
    labels: ['this-is-an-extremely-long-label-that-by-itself-exceeds-the-200-character-limit-for-blogger-api-and-should-be-truncated-to-fit-within-the-constraints-imposed-by-the-platform-for-optimal-performance'],
    expectedPass: true
  }
];

function testLabelValidation() {
  console.log('🧪 Testing label validation functionality...\n');

  testCases.forEach((testCase, index) => {
    console.log(`Test ${index + 1}: ${testCase.name}`);
    console.log('Input labels:', testCase.labels);
    
    try {
      const validatedLabels = bloggerService.validateAndTruncateLabels(testCase.labels);
      const totalLength = validatedLabels.join('').length;
      
      console.log('✅ Validation result:', {
        outputLabels: validatedLabels,
        labelCount: validatedLabels.length,
        totalLength: totalLength,
        withinLimit: totalLength <= 200
      });
      
      if (totalLength > 200) {
        console.log('❌ FAILED: Total length exceeds 200 characters');
      } else {
        console.log('✅ PASSED: Labels are within limits');
      }
      
    } catch (error) {
      console.log('❌ ERROR:', error.message);
    }
    
    console.log('---\n');
  });
}

// Only run if this file is executed directly
if (require.main === module) {
  testLabelValidation();
}

module.exports = { testLabelValidation };
