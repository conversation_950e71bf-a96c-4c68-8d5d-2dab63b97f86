const axios = require('axios');
const config = require('../config/config');

class TelegramService {
  constructor() {
    this.botToken = process.env.TELEGRAM_BOT_TOKEN || '**********************************************';
    this.chatId = process.env.TELEGRAM_CHAT_ID || '-1001478643154';
    this.baseUrl = `https://api.telegram.org/bot${this.botToken}`;
    
    console.log('TelegramService initialized:', {
      hasBotToken: !!this.botToken,
      chatId: this.chatId,
      baseUrl: this.baseUrl.replace(this.botToken, 'BOT_TOKEN_HIDDEN')
    });
  }

  /**
   * Detect if article content is job-related
   * @param {string} title - Article title
   * @param {string} content - Article content
   * @returns {boolean} True if job-related
   */
  isJobRelated(title, content) {
    const jobKeywords = [
      'job', 'jobs', 'hiring', 'recruitment', 'vacancy', 'vacancies',
      'position', 'opening', 'career', 'careers', 'employment',
      'apply', 'candidate', 'interview', 'salary', 'work',
      'company', 'pune', 'mumbai', 'bangalore', 'delhi',
      'software engineer', 'developer', 'programmer', 'analyst',
      'manager', 'executive', 'associate', 'intern', 'fresher',
      'experience', 'skills', 'qualification', 'resume'
    ];

    const textToCheck = `${title} ${content}`.toLowerCase();
    return jobKeywords.some(keyword => textToCheck.includes(keyword));
  }

  /**
   * Extract important lines from article content
   * @param {string} content - HTML content
   * @param {number} maxLines - Maximum lines to extract (default: 3)
   * @returns {string} Extracted summary
   */
  extractSummary(content, maxLines = 3) {
    try {
      // Remove HTML tags and get clean text
      const cleanText = content
        .replace(/<[^>]*>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

      // Split into sentences
      const sentences = cleanText
        .split(/[.!?]+/)
        .map(s => s.trim())
        .filter(s => s.length > 20) // Filter out very short sentences
        .slice(0, maxLines);

      return sentences.join('. ') + (sentences.length > 0 ? '.' : '');
    } catch (error) {
      console.error('Error extracting summary:', error);
      return 'Check out this interesting article...';
    }
  }

  /**
   * Generate formatted Telegram message
   * @param {Object} articleData - Article data
   * @returns {string} Formatted message
   */
  generateMessage(articleData) {
    const { title, content, url, labels = [] } = articleData;
    
    // Detect if job-related
    const isJob = this.isJobRelated(title, content);
    
    // Extract summary
    const summary = this.extractSummary(content);
    
    // Select appropriate emojis
    const emojis = isJob 
      ? ['💼', '🚀', '💰', '🎯', '📈', '🏢']
      : ['📰', '✨', '🔥', '💡', '📚', '🌟'];
    
    const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];
    
    // Build message
    let message = `${randomEmoji} *${title}*\n\n`;
    
    if (summary) {
      message += `${summary}\n\n`;
    }
    
    if (isJob) {
      message += `💼 *Apply here:* `;
    } else {
      message += `📖 *Read more:* `;
    }
    
    message += `[Click here](${url})`;
    
    // Add hashtags from labels
    if (labels && labels.length > 0) {
      const hashtags = labels
        .slice(0, 5) // Limit to 5 hashtags
        .map(label => `#${label.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '')}`)
        .filter(tag => tag.length > 1)
        .join(' ');
      
      if (hashtags) {
        message += `\n\n${hashtags}`;
      }
    }
    
    // Add channel promotion
    message += `\n\n🔔 Join us: @jobs_in_pune`;
    
    return message;
  }

  /**
   * Send message to Telegram
   * @param {string} message - Message to send
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} API response
   */
  async sendMessage(message, options = {}) {
    try {
      console.log('Sending message to Telegram:', {
        chatId: this.chatId,
        messageLength: message.length,
        parseMode: options.parse_mode || 'Markdown'
      });

      const payload = {
        chat_id: this.chatId,
        text: message,
        parse_mode: options.parse_mode || 'Markdown',
        disable_web_page_preview: options.disable_web_page_preview || false,
        disable_notification: options.disable_notification || false,
        ...options
      };

      const response = await axios.post(`${this.baseUrl}/sendMessage`, payload);
      
      console.log('Telegram message sent successfully:', {
        messageId: response.data.result.message_id,
        chatId: response.data.result.chat.id,
        date: new Date(response.data.result.date * 1000).toISOString()
      });

      return {
        success: true,
        data: response.data.result,
        messageId: response.data.result.message_id
      };

    } catch (error) {
      console.error('Telegram send message error:', {
        message: error.message,
        code: error.code,
        response: error.response?.data
      });

      let errorMessage = 'Failed to send message to Telegram';
      
      if (error.response?.data?.description) {
        errorMessage = error.response.data.description;
      } else if (error.message) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  }

  /**
   * Generate and send article notification
   * @param {Object} articleData - Article data
   * @param {Object} options - Send options
   * @returns {Promise<Object>} Result
   */
  async shareArticle(articleData, options = {}) {
    try {
      console.log('TelegramService.shareArticle called:', {
        title: articleData.title,
        hasUrl: !!articleData.url,
        hasContent: !!articleData.content,
        labelsCount: articleData.labels?.length || 0
      });

      // Generate message
      const message = this.generateMessage(articleData);
      
      // Send message
      const result = await this.sendMessage(message, options);
      
      return {
        success: true,
        message: 'Article shared to Telegram successfully',
        data: result.data,
        messageId: result.messageId,
        generatedMessage: message
      };

    } catch (error) {
      console.error('TelegramService.shareArticle error:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to share article to Telegram'
      };
    }
  }

  /**
   * Test bot connection
   * @returns {Promise<Object>} Bot info
   */
  async testConnection() {
    try {
      const response = await axios.get(`${this.baseUrl}/getMe`);
      return {
        success: true,
        botInfo: response.data.result
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new TelegramService();
