const { GoogleGenerativeAI } = require('@google/generative-ai');
const config = require('../config/config');

class GeminiService {
  constructor() {
    if (!config.geminiApiKey) {
      throw new Error('Gemini API key is not configured');
    }
    
    this.genAI = new GoogleGenerativeAI(config.geminiApiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
  }

  async rewriteContent({ content, title, articleType = 'general', customPrompt }) {
    try {
      if (!content) {
        throw new Error('Content is required for rewriting');
      }

      // Detect the language of the content
      const detectedLanguage = await this.detectLanguage(content);

      const prompt = this.buildPrompt({ content, title, articleType, customPrompt, language: detectedLanguage });

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const rewrittenText = response.text();

      // Parse the response to extract structured data
      const parsedContent = this.parseRewrittenContent(rewrittenText, title);

      return {
        success: true,
        originalWordCount: content.split(/\s+/).length,
        rewrittenWordCount: parsedContent.content.split(/\s+/).length,
        detectedLanguage: detectedLanguage,
        ...parsedContent,
        rewrittenAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('Gemini API error:', error);

      if (error.message.includes('API key')) {
        throw new Error('Invalid or missing Gemini API key');
      } else if (error.message.includes('quota')) {
        throw new Error('API quota exceeded. Please try again later.');
      } else if (error.message.includes('safety')) {
        throw new Error('Content was flagged by safety filters. Please try different content.');
      } else {
        throw new Error(`Content rewriting failed: ${error.message}`);
      }
    }
  }

  async humanizeContent({ content, title, language }) {
    try {
      if (!content) {
        throw new Error('Content is required for humanization');
      }

      console.log('Starting content humanization with Gemini API...');
      console.log('Content length:', content.length);
      console.log('Target language:', language || 'auto-detect');

      // Detect language if not provided
      const detectedLanguage = language || await this.detectLanguage(content);

      const prompt = this.buildHumanizePrompt({ content, title, language: detectedLanguage });

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const humanizedText = response.text();

      // Parse the response to extract structured data
      const parsedContent = this.parseHumanizedContent(humanizedText, title);

      console.log('Content humanization completed successfully');
      console.log('Original length:', content.length);
      console.log('Humanized length:', parsedContent.content.length);

      return {
        success: true,
        originalWordCount: content.split(/\s+/).length,
        humanizedWordCount: parsedContent.content.split(/\s+/).length,
        detectedLanguage: detectedLanguage,
        ...parsedContent,
        humanizedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('Gemini API humanization error:', error);

      if (error.message.includes('API key')) {
        throw new Error('Invalid or missing Gemini API key');
      } else if (error.message.includes('quota')) {
        throw new Error('API quota exceeded. Please try again later.');
      } else if (error.message.includes('safety')) {
        throw new Error('Content was flagged by safety filters. Please try different content.');
      } else {
        throw new Error(`Content humanization failed: ${error.message}`);
      }
    }
  }

  async detectLanguage(content) {
    try {
      const languageDetectionPrompt = `Analyze the following text and identify its primary language. Respond with only the language name in English (e.g., "English", "Hindi", "Marathi", "Spanish", "French", etc.).

Text to analyze:
${content.substring(0, 500)}...

Language:`;

      const result = await this.model.generateContent(languageDetectionPrompt);
      const response = await result.response;
      const detectedLanguage = response.text().trim();

      // Validate and normalize the detected language
      const normalizedLanguage = this.normalizeLanguage(detectedLanguage);

      return normalizedLanguage;
    } catch (error) {
      console.warn('Language detection failed, defaulting to English:', error);
      return 'English';
    }
  }

  normalizeLanguage(language) {
    const languageMap = {
      'hindi': 'Hindi',
      'marathi': 'Marathi',
      'english': 'English',
      'spanish': 'Spanish',
      'french': 'French',
      'german': 'German',
      'italian': 'Italian',
      'portuguese': 'Portuguese',
      'russian': 'Russian',
      'chinese': 'Chinese',
      'japanese': 'Japanese',
      'korean': 'Korean',
      'arabic': 'Arabic',
      'bengali': 'Bengali',
      'tamil': 'Tamil',
      'telugu': 'Telugu',
      'gujarati': 'Gujarati',
      'punjabi': 'Punjabi',
      'urdu': 'Urdu'
    };

    const normalized = languageMap[language.toLowerCase()] || language;
    return normalized || 'English';
  }

  buildPrompt({ content, title, articleType, customPrompt, language = 'English' }) {
    const languageInstruction = language !== 'English'
      ? `\n\nIMPORTANT: The original content is in ${language}. You MUST rewrite the content in the SAME language (${language}). Preserve all language-specific nuances, cultural context, and writing style typical of ${language}. Do not translate to English or any other language.`
      : '';

    const basePrompt = `You are an expert content writer and SEO specialist. Your task is to rewrite the following article to be completely plagiarism-free, SEO-optimized, and engaging while maintaining the original meaning and key information.

ARTICLE TYPE: ${articleType}
ORIGINAL TITLE: ${title || 'No title provided'}
CONTENT LANGUAGE: ${language}${languageInstruction}

REQUIREMENTS:
1. Rewrite the content to be 100% unique and plagiarism-free
2. Optimize for SEO with proper keyword usage and structure
3. Create engaging, readable content that flows naturally
4. Maintain all important facts and information from the original
5. Use proper HTML structure with H1, H2, H3 tags where appropriate
6. Generate a compelling, SEO-friendly title in ${language}
7. Create a meta description (150-160 characters) in ${language}
8. Suggest relevant tags/keywords in ${language}
9. Suggest a permalink/URL slug (can be in English for URL compatibility)
10. CRITICAL: Write ALL content in ${language} - do not change the language
11. Preserve cultural context and language-specific expressions
12. Maintain the tone and style appropriate for ${language} readers

ORIGINAL CONTENT:
${content}

${customPrompt ? `ADDITIONAL INSTRUCTIONS: ${customPrompt}` : ''}

Please provide your response in the following JSON format (with content in ${language}):
{
  "title": "SEO-optimized title in ${language}",
  "content": "Rewritten HTML content with proper H1, H2, H3 structure in ${language}",
  "metaDescription": "SEO meta description (150-160 chars) in ${language}",
  "tags": ["tag1", "tag2", "tag3"],
  "permalink": "seo-friendly-url-slug",
  "summary": "Brief summary of the article in ${language}"
}`;

    return basePrompt;
  }

  buildHumanizePrompt({ content, title, language = 'English' }) {
    const languageInstruction = language !== 'English'
      ? `\n\nIMPORTANT: The content is in ${language}. You MUST maintain the SAME language (${language}). Do not translate to English or any other language.`
      : '';

    const humanizePrompt = `You are an expert content humanizer and AI detection bypass specialist. Your task is to transform the following AI-generated content to make it appear completely human-written while maintaining all the original meaning, information, and structure.

TITLE: ${title || 'No title provided'}
CONTENT LANGUAGE: ${language}${languageInstruction}

HUMANIZATION OBJECTIVES:
1. REDUCE AI DETECTION: Make the content undetectable by AI detection tools (target: <5% AI detection score)
2. NATURAL FLOW: Create natural, human-like writing patterns with varied sentence structures
3. AUTHENTIC VOICE: Add subtle imperfections and natural variations that humans typically include
4. PRESERVE MEANING: Keep all original information, facts, and key points intact
5. MAINTAIN SEO: Preserve SEO optimization and keyword placement
6. KEEP STRUCTURE: Maintain the HTML structure and formatting

HUMANIZATION TECHNIQUES TO APPLY:
- Vary sentence lengths (mix short, medium, and long sentences)
- Use natural transitions and connective phrases
- Add subtle personal touches and conversational elements
- Include minor grammatical variations that humans naturally use
- Vary paragraph lengths and structures
- Use synonyms and alternative phrasings
- Add natural hesitations or qualifiers (e.g., "perhaps", "it seems", "generally")
- Include rhetorical questions where appropriate
- Use contractions naturally
- Add subtle emotional undertones

CONTENT TO HUMANIZE:
${content}

Please provide your response in the following JSON format (with content in ${language}):
{
  "title": "Humanized title in ${language}",
  "content": "Fully humanized HTML content with natural flow in ${language}",
  "metaDescription": "Humanized meta description (150-160 chars) in ${language}",
  "tags": ["tag1", "tag2", "tag3"],
  "permalink": "humanized-url-slug",
  "summary": "Humanized summary of the article in ${language}",
  "humanizationNotes": "Brief notes on what was changed to make it more human-like"
}`;

    return humanizePrompt;
  }

  parseRewrittenContent(rewrittenText, originalTitle) {
    try {
      // Try to parse as JSON first
      const jsonMatch = rewrittenText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return this.validateAndCleanParsedContent(parsed, originalTitle);
      }
    } catch (error) {
      console.warn('Failed to parse JSON response, using fallback parsing');
    }

    // Fallback parsing if JSON parsing fails
    return this.fallbackParsing(rewrittenText, originalTitle);
  }

  validateAndCleanParsedContent(parsed, originalTitle) {
    return {
      title: parsed.title || originalTitle || 'Untitled Article',
      content: this.ensureHtmlStructure(parsed.content || ''),
      metaDescription: (parsed.metaDescription || '').substring(0, 160),
      tags: Array.isArray(parsed.tags) ? parsed.tags.slice(0, 10) : [],
      permalink: this.generatePermalink(parsed.permalink || parsed.title || originalTitle),
      summary: parsed.summary || ''
    };
  }

  parseHumanizedContent(humanizedText, originalTitle) {
    try {
      // Try to parse as JSON first
      const jsonMatch = humanizedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return this.validateAndCleanHumanizedContent(parsed, originalTitle);
      }
    } catch (error) {
      console.warn('Failed to parse JSON response for humanized content, using fallback parsing');
    }

    // Fallback parsing if JSON parsing fails
    return this.fallbackParsing(humanizedText, originalTitle);
  }

  validateAndCleanHumanizedContent(parsed, originalTitle) {
    return {
      title: parsed.title || originalTitle || 'Untitled Article',
      content: this.ensureHtmlStructure(parsed.content || ''),
      metaDescription: (parsed.metaDescription || '').substring(0, 160),
      tags: Array.isArray(parsed.tags) ? parsed.tags.slice(0, 10) : [],
      permalink: this.generatePermalink(parsed.permalink || parsed.title || originalTitle),
      summary: parsed.summary || '',
      humanizationNotes: parsed.humanizationNotes || 'Content humanized to reduce AI detection'
    };
  }

  fallbackParsing(text, originalTitle) {
    // Extract title (look for first line or heading)
    const lines = text.split('\n').filter(line => line.trim());
    const title = lines[0]?.replace(/^#+\s*/, '').trim() || originalTitle || 'Untitled Article';
    
    // Use the entire text as content
    const content = this.ensureHtmlStructure(text);
    
    // Generate basic metadata
    const metaDescription = text.substring(0, 150).replace(/\n/g, ' ').trim() + '...';
    const permalink = this.generatePermalink(title);
    
    return {
      title,
      content,
      metaDescription,
      tags: [],
      permalink,
      summary: metaDescription
    };
  }

  ensureHtmlStructure(content) {
    if (!content) return '';
    
    // If content already has HTML tags, return as is
    if (content.includes('<h1>') || content.includes('<h2>') || content.includes('<p>')) {
      return content;
    }
    
    // Convert plain text to HTML structure
    const lines = content.split('\n').filter(line => line.trim());
    let html = '';
    let inParagraph = false;
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;
      
      // Check if line looks like a heading
      if (trimmed.length < 100 && (
        trimmed.endsWith(':') || 
        trimmed.match(/^[A-Z][^.!?]*$/) ||
        trimmed.startsWith('#')
      )) {
        if (inParagraph) {
          html += '</p>\n';
          inParagraph = false;
        }
        const headingText = trimmed.replace(/^#+\s*/, '').replace(/:$/, '');
        html += `<h2>${headingText}</h2>\n`;
      } else {
        if (!inParagraph) {
          html += '<p>';
          inParagraph = true;
        } else {
          html += ' ';
        }
        html += trimmed;
      }
    }
    
    if (inParagraph) {
      html += '</p>';
    }
    
    return html;
  }

  generatePermalink(text) {
    if (!text) return 'article';
    
    return text
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50);
  }

  async generateSEOSuggestions(content, title) {
    try {
      const prompt = `Analyze the following content and provide SEO suggestions:

TITLE: ${title}
CONTENT: ${content}

Please provide:
1. Keyword suggestions (5-10 keywords)
2. Content optimization tips
3. Readability improvements
4. Meta tag suggestions

Respond in JSON format:
{
  "keywords": ["keyword1", "keyword2"],
  "suggestions": ["tip1", "tip2"],
  "readabilityScore": "Good/Fair/Poor",
  "metaTags": {
    "title": "optimized title",
    "description": "meta description"
  }
}`;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const suggestions = response.text();

      try {
        return JSON.parse(suggestions);
      } catch (e) {
        return { error: 'Failed to parse SEO suggestions' };
      }

    } catch (error) {
      console.error('SEO suggestions error:', error);
      return { error: error.message };
    }
  }
}

module.exports = new GeminiService();
