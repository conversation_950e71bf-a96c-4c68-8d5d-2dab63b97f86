const config = require('../config/config');

const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Default error
  let error = {
    message: 'Internal Server Error',
    status: 500
  };

  // Handle specific error types
  if (err.name === 'ValidationError') {
    error.message = 'Validation Error';
    error.status = 400;
    error.details = err.message;
  } else if (err.name === 'UnauthorizedError') {
    error.message = 'Unauthorized';
    error.status = 401;
  } else if (err.code === 'ENOTFOUND' || err.code === 'ECONNREFUSED') {
    error.message = 'Network Error';
    error.status = 503;
    error.details = 'Unable to connect to external service';
  } else if (err.status) {
    error.status = err.status;
    error.message = err.message;
  }

  // Send JSON response for API routes
  if (req.path.startsWith('/api/')) {
    const response = {
      success: false,
      error: error.message
    };

    // Include more details for debugging in production for specific routes
    if (config.nodeEnv === 'production' && req.path.includes('/publish')) {
      response.debugInfo = {
        timestamp: new Date().toISOString(),
        path: req.path,
        method: req.method,
        errorType: err.name,
        errorCode: err.code
      };
    } else if (config.nodeEnv === 'development') {
      response.details = error.details;
      response.stack = err.stack;
    }

    return res.status(error.status).json(response);
  }

  // Don't leak error details in production for web routes
  if (config.nodeEnv === 'production') {
    delete error.details;
    if (error.status === 500) {
      error.message = 'Internal Server Error';
    }
  }

  // Render error page for web routes
  res.status(error.status).render('error', {
    title: 'Error',
    message: error.message,
    error: config.nodeEnv === 'development' ? err : {}
  });
};

module.exports = errorHandler;
