require('dotenv').config();

const config = {
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // API Keys
  geminiApiKey: process.env.GEMINI_API_KEY,
  
  // Rate Limiting
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  
  // Scraping Configuration
  scrapingTimeout: parseInt(process.env.SCRAPING_TIMEOUT) || 30000, // 30 seconds
  maxContentLength: parseInt(process.env.MAX_CONTENT_LENGTH) || 50000,
  
  // Session Configuration
  sessionSecret: process.env.SESSION_SECRET || 'fallback-secret-key',
  
  // Google OAuth Configuration
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    redirectUri: process.env.BLOGGER_REDIRECT_URI ||
      (process.env.NODE_ENV === 'production'
        ? 'https://automated-blogging.onrender.com/auth/blogger/callback'
        : 'http://localhost:3000/auth/blogger/callback')
  },

  // Telegram Configuration
  telegram: {
    botToken: process.env.TELEGRAM_BOT_TOKEN || '**********************************************',
    chatId: process.env.TELEGRAM_CHAT_ID || '-1001478643154',
    groupName: 'Pune private companies jobs 👍',
    username: 'jobs_in_pune'
  },

  // CORS Configuration
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  
  // Validation
  isValid() {
    const required = [
      'geminiApiKey',
      'google.clientId',
      'google.clientSecret'
    ];
    
    for (const key of required) {
      const value = key.includes('.') 
        ? key.split('.').reduce((obj, k) => obj?.[k], this)
        : this[key];
      
      if (!value) {
        console.error(`Missing required configuration: ${key}`);
        return false;
      }
    }
    return true;
  }
};

module.exports = config;
