const express = require('express');
const passport = require('passport');
const router = express.Router();

// Google OAuth routes
router.get('/google', passport.authenticate('google', {
  scope: ['profile', 'email', 'https://www.googleapis.com/auth/blogger']
}));

router.get('/blogger/callback',
  (req, res, next) => {
    console.log('OAuth callback received:', {
      query: req.query,
      url: req.url,
      originalUrl: req.originalUrl,
      headers: {
        host: req.headers.host,
        'x-forwarded-proto': req.headers['x-forwarded-proto']
      }
    });
    next();
  },
  passport.authenticate('google', {
    failureRedirect: '/?auth=error',
    failureFlash: false
  }),
  (req, res) => {
    console.log('OAuth authentication successful:', {
      user: req.user ? { id: req.user.id, name: req.user.displayName } : 'No user'
    });
    // Successful authentication
    res.redirect('/?auth=success');
  }
);

// Logout route
router.get('/logout', (req, res) => {
  req.logout((err) => {
    if (err) {
      console.error('Logout error:', err);
    }
    res.redirect('/');
  });
});

// Check authentication status
router.get('/status', (req, res) => {
  res.json({
    authenticated: !!req.user,
    user: req.user ? {
      id: req.user.id,
      name: req.user.displayName,
      email: req.user.emails?.[0]?.value
    } : null
  });
});

// OAuth configuration debug endpoint (remove in production)
router.get('/debug', (req, res) => {
  const config = require('../config/config');
  res.json({
    nodeEnv: config.nodeEnv,
    redirectUri: config.google.redirectUri,
    clientIdSet: !!config.google.clientId,
    clientSecretSet: !!config.google.clientSecret,
    host: req.headers.host,
    protocol: req.headers['x-forwarded-proto'] || req.protocol,
    fullUrl: `${req.headers['x-forwarded-proto'] || req.protocol}://${req.headers.host}${req.originalUrl}`,
    user: req.user ? {
      id: req.user.id,
      hasAccessToken: !!req.user.accessToken,
      hasRefreshToken: !!req.user.refreshToken,
      tokenLength: req.user.accessToken?.length || 0
    } : null
  });
});

// Test publishing endpoint (remove in production)
router.get('/test-publish', (req, res) => {
  if (!req.user) {
    return res.json({
      error: 'Not authenticated',
      message: 'Please log in first'
    });
  }

  res.json({
    message: 'User is authenticated',
    user: {
      id: req.user.id,
      displayName: req.user.displayName,
      email: req.user.emails?.[0]?.value,
      hasAccessToken: !!req.user.accessToken,
      hasRefreshToken: !!req.user.refreshToken,
      accessTokenLength: req.user.accessToken?.length || 0,
      refreshTokenLength: req.user.refreshToken?.length || 0
    },
    nextStep: 'Try making a POST request to /api/articles/publish with test data'
  });
});

// Test publishing page (remove in production)
router.get('/test-page', (req, res) => {
  if (!req.user) {
    return res.redirect('/');
  }

  const testHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>Publishing Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .user-info { background: #e2e3e5; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Publishing Test Page</h1>

        <div class="user-info">
            <h3>User Info:</h3>
            <p><strong>Name:</strong> ${req.user.displayName}</p>
            <p><strong>Email:</strong> ${req.user.emails?.[0]?.value}</p>
            <p><strong>Access Token:</strong> ${req.user.accessToken ? 'Present (' + req.user.accessToken.length + ' chars)' : 'Missing'}</p>
            <p><strong>Refresh Token:</strong> ${req.user.refreshToken ? 'Present (' + req.user.refreshToken.length + ' chars)' : 'Missing'}</p>
        </div>

        <form id="publishForm">
            <div class="form-group">
                <label for="blogId">Blog ID:</label>
                <input type="text" id="blogId" name="blogId" placeholder="Enter your Blogger blog ID" required>
            </div>

            <div class="form-group">
                <label for="title">Title:</label>
                <input type="text" id="title" name="title" value="Test Article - Debug Publishing" required>
            </div>

            <div class="form-group">
                <label for="content">Content:</label>
                <textarea id="content" name="content" rows="6" required><p>This is a test article for debugging the publishing functionality.</p><p>If you see this published on your blog, the publishing is working correctly!</p></textarea>
            </div>

            <div class="form-group">
                <label for="labels">Labels (comma-separated):</label>
                <input type="text" id="labels" name="labels" value="test,debug">
            </div>

            <div class="form-group">
                <label for="metaDescription">Meta Description:</label>
                <input type="text" id="metaDescription" name="metaDescription" value="Test article for debugging publishing functionality">
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="isDraft" name="isDraft" checked> Save as Draft (recommended for testing)
                </label>
            </div>

            <button type="submit">Test Publishing</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        document.getElementById('publishForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {
                blogId: formData.get('blogId'),
                title: formData.get('title'),
                content: formData.get('content'),
                labels: formData.get('labels').split(',').map(l => l.trim()).filter(l => l),
                metaDescription: formData.get('metaDescription'),
                isDraft: formData.has('isDraft')
            };

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Publishing...</p>';

            try {
                const response = await fetch('/api/articles/publish', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>✅ Publishing Successful!</h3>
                            <p><strong>Post ID:</strong> \${result.data.id}</p>
                            <p><strong>URL:</strong> <a href="\${result.data.url}" target="_blank">\${result.data.url}</a></p>
                            <p><strong>Status:</strong> \${result.data.status}</p>
                        </div>
                    \`;
                } else {
                    resultDiv.innerHTML = \`
                        <div class="result error">
                            <h3>❌ Publishing Failed</h3>
                            <p><strong>Error:</strong> \${result.error}</p>
                            <p><strong>Details:</strong> \${JSON.stringify(result, null, 2)}</p>
                            <p><strong>Check the server console for detailed logs!</strong></p>
                        </div>
                    \`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`
                    <div class="result error">
                        <h3>❌ Request Failed</h3>
                        <p><strong>Error:</strong> \${error.message}</p>
                        <p><strong>Check the server console for detailed logs!</strong></p>
                    </div>
                \`;
            }
        });
    </script>
</body>
</html>
  `;

  res.send(testHtml);
});

module.exports = router;
