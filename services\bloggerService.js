const { google } = require('googleapis');
const config = require('../config/config');

class BloggerService {
  constructor() {
    this.oauth2Client = new google.auth.OAuth2(
      config.google.clientId,
      config.google.clientSecret,
      config.google.redirectUri
    );
    
    this.blogger = google.blogger({ version: 'v3', auth: this.oauth2Client });
  }

  async setUserCredentials(user) {
    console.log('Setting user credentials:', {
      userId: user?.id,
      hasAccessToken: !!user?.accessToken,
      hasRefreshToken: !!user?.refreshToken,
      accessTokenLength: user?.accessToken?.length || 0
    });

    if (!user.accessToken) {
      console.error('User access token missing:', {
        userId: user?.id,
        userKeys: Object.keys(user || {})
      });
      throw new Error('User access token not found');
    }

    this.oauth2Client.setCredentials({
      access_token: user.accessToken,
      refresh_token: user.refreshToken
    });

    // Try to refresh the token if it's expired
    try {
      if (user.refreshToken) {
        console.log('Attempting to refresh access token...');
        const { credentials } = await this.oauth2Client.refreshAccessToken();
        console.log('Token refresh successful:', {
          hasNewAccessToken: !!credentials.access_token,
          tokenType: credentials.token_type
        });

        // Update the user object with new tokens (this won't persist to session automatically)
        if (credentials.access_token) {
          user.accessToken = credentials.access_token;
        }
      } else {
        console.warn('No refresh token available - token refresh not possible');
        console.warn('Note: If authentication issues occur, user may need to re-authenticate');
      }
    } catch (refreshError) {
      console.warn('Token refresh failed (will continue with existing token):', {
        error: refreshError.message,
        code: refreshError.code
      });
      // Continue with existing token - it might still be valid
    }
  }

  async getUserBlogs(user) {
    try {
      await this.setUserCredentials(user);
      
      const response = await this.blogger.blogs.listByUser({
        userId: 'self'
      });
      
      const blogs = response.data.items || [];
      
      return blogs.map(blog => ({
        id: blog.id,
        name: blog.name,
        description: blog.description,
        url: blog.url,
        status: blog.status,
        posts: blog.posts?.totalItems || 0,
        updated: blog.updated
      }));
      
    } catch (error) {
      console.error('Error fetching user blogs:', error);
      
      if (error.code === 401) {
        throw new Error('Authentication expired. Please log in again.');
      } else if (error.code === 403) {
        throw new Error('Access denied. Please ensure you have granted Blogger permissions.');
      } else {
        throw new Error(`Failed to fetch blogs: ${error.message}`);
      }
    }
  }

  async publishPost(user, postData) {
    try {
      console.log('BloggerService.publishPost called with:', {
        userId: user?.id,
        postDataKeys: Object.keys(postData || {}),
        blogId: postData?.blogId,
        titleLength: postData?.title?.length || 0,
        contentLength: postData?.content?.length || 0
      });

      await this.setUserCredentials(user);

      const { blogId, title, content, labels, customUrl, metaDescription, isDraft = false } = postData;

      if (!blogId || !title || !content) {
        console.error('Missing required fields:', { blogId: !!blogId, title: !!title, content: !!content });
        throw new Error('Blog ID, title, and content are required');
      }

      console.log('Enhancing content for Blogger...');
      // Enhanced content formatting for better SEO and styling preservation
      const enhancedContent = this.enhanceContentForBlogger(content, metaDescription);

      // Validate and truncate labels to meet Blogger's 200-character limit
      const validatedLabels = this.validateAndTruncateLabels(labels || []);

      // Prepare post object with enhanced SEO features
      const post = {
        title: title,
        content: enhancedContent,
        labels: validatedLabels
      };

      // Add custom URL if provided (for SEO-friendly URLs)
      if (customUrl) {
        post.url = customUrl;
      }

      console.log('Prepared post object:', {
        titleLength: post.title.length,
        contentLength: post.content.length,
        labelsCount: post.labels.length,
        hasCustomUrl: !!post.url,
        isDraft
      });

      let response;

      if (isDraft) {
        console.log('Creating draft post...');
        // Create as draft
        response = await this.blogger.posts.insert({
          blogId: blogId,
          isDraft: true,
          requestBody: post
        });
      } else {
        console.log('Publishing post immediately...');
        // Publish immediately
        response = await this.blogger.posts.insert({
          blogId: blogId,
          requestBody: post
        });
      }

      console.log('Blogger API response received:', {
        status: response.status,
        postId: response.data?.id,
        postStatus: response.data?.status
      });

      const publishedPost = response.data;

      return {
        id: publishedPost.id,
        title: publishedPost.title,
        url: publishedPost.url,
        published: publishedPost.published,
        status: publishedPost.status,
        labels: publishedPost.labels || [],
        blogId: blogId
      };

    } catch (error) {
      console.error('BloggerService.publishPost error:', {
        message: error.message,
        code: error.code,
        status: error.status,
        response: error.response?.data,
        stack: error.stack
      });

      if (error.code === 401) {
        throw new Error('Authentication expired. Please log in again.');
      } else if (error.code === 403) {
        throw new Error('Access denied. You may not have permission to post to this blog.');
      } else if (error.code === 400) {
        // Provide specific error messages for common 400 errors
        const errorMessage = error.message || '';
        if (errorMessage.includes('labels must be at most 200 characters')) {
          throw new Error('Labels are too long. The total length of all labels must be at most 200 characters.');
        } else if (errorMessage.includes('title')) {
          throw new Error('Invalid title. Please check your post title and try again.');
        } else if (errorMessage.includes('content')) {
          throw new Error('Invalid content. Please check your post content and try again.');
        } else if (errorMessage.includes('blogId')) {
          throw new Error('Invalid blog ID. Please check your blog ID and try again.');
        } else {
          throw new Error(`Invalid post data: ${errorMessage}`);
        }
      } else {
        throw new Error(`Failed to publish post: ${error.message}`);
      }
    }
  }

  async updatePost(user, postData) {
    try {
      await this.setUserCredentials(user);
      
      const { blogId, postId, title, content, labels, customUrl } = postData;
      
      if (!blogId || !postId) {
        throw new Error('Blog ID and Post ID are required');
      }

      const post = {
        title: title,
        content: content,
        labels: labels || []
      };

      if (customUrl) {
        post.url = customUrl;
      }

      const response = await this.blogger.posts.update({
        blogId: blogId,
        postId: postId,
        requestBody: post
      });
      
      const updatedPost = response.data;
      
      return {
        id: updatedPost.id,
        title: updatedPost.title,
        url: updatedPost.url,
        published: updatedPost.published,
        updated: updatedPost.updated,
        status: updatedPost.status,
        labels: updatedPost.labels || []
      };
      
    } catch (error) {
      console.error('Error updating post:', error);
      throw new Error(`Failed to update post: ${error.message}`);
    }
  }

  async deletePost(user, blogId, postId) {
    try {
      await this.setUserCredentials(user);
      
      await this.blogger.posts.delete({
        blogId: blogId,
        postId: postId
      });
      
      return { success: true, message: 'Post deleted successfully' };
      
    } catch (error) {
      console.error('Error deleting post:', error);
      throw new Error(`Failed to delete post: ${error.message}`);
    }
  }

  async getPost(user, blogId, postId) {
    try {
      await this.setUserCredentials(user);
      
      const response = await this.blogger.posts.get({
        blogId: blogId,
        postId: postId
      });
      
      const post = response.data;
      
      return {
        id: post.id,
        title: post.title,
        content: post.content,
        url: post.url,
        published: post.published,
        updated: post.updated,
        status: post.status,
        labels: post.labels || [],
        author: post.author
      };
      
    } catch (error) {
      console.error('Error fetching post:', error);
      throw new Error(`Failed to fetch post: ${error.message}`);
    }
  }

  async getBlogPosts(user, blogId, options = {}) {
    try {
      await this.setUserCredentials(user);
      
      const { maxResults = 10, pageToken, status = 'published' } = options;
      
      const response = await this.blogger.posts.list({
        blogId: blogId,
        maxResults: maxResults,
        pageToken: pageToken,
        status: status
      });
      
      const posts = response.data.items || [];
      
      return {
        posts: posts.map(post => ({
          id: post.id,
          title: post.title,
          url: post.url,
          published: post.published,
          updated: post.updated,
          status: post.status,
          labels: post.labels || []
        })),
        nextPageToken: response.data.nextPageToken,
        totalItems: response.data.totalItems
      };
      
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      throw new Error(`Failed to fetch blog posts: ${error.message}`);
    }
  }

  async validateBlogAccess(user, blogId) {
    try {
      await this.setUserCredentials(user);
      
      const response = await this.blogger.blogs.get({
        blogId: blogId
      });
      
      return {
        hasAccess: true,
        blog: {
          id: response.data.id,
          name: response.data.name,
          url: response.data.url
        }
      };
      
    } catch (error) {
      return {
        hasAccess: false,
        error: error.message
      };
    }
  }

  /**
   * Validate and truncate labels to meet Blogger's 200-character limit
   * @param {Array} labels - Array of label strings
   * @returns {Array} Validated and truncated labels
   */
  validateAndTruncateLabels(labels) {
    if (!Array.isArray(labels) || labels.length === 0) {
      return [];
    }

    // Clean and filter labels
    let cleanLabels = labels
      .map(label => String(label).trim())
      .filter(label => label.length > 0)
      .map(label => label.substring(0, 50)); // Individual label max 50 chars

    // Calculate total length and truncate if necessary
    let totalLength = cleanLabels.join('').length;
    const maxTotalLength = 200;

    if (totalLength <= maxTotalLength) {
      console.log('Labels validation passed:', {
        labelCount: cleanLabels.length,
        totalLength: totalLength,
        labels: cleanLabels
      });
      return cleanLabels;
    }

    // Truncate labels to fit within 200 characters
    const truncatedLabels = [];
    let currentLength = 0;

    for (const label of cleanLabels) {
      if (currentLength + label.length <= maxTotalLength) {
        truncatedLabels.push(label);
        currentLength += label.length;
      } else {
        // Try to fit a shortened version of the label
        const remainingSpace = maxTotalLength - currentLength;
        if (remainingSpace > 3) { // Minimum meaningful label length
          truncatedLabels.push(label.substring(0, remainingSpace));
        }
        break;
      }
    }

    console.log('Labels truncated to fit Blogger limits:', {
      originalCount: cleanLabels.length,
      truncatedCount: truncatedLabels.length,
      originalLength: totalLength,
      truncatedLength: truncatedLabels.join('').length,
      truncatedLabels: truncatedLabels
    });

    return truncatedLabels;
  }

  /**
   * Enhance content for better Blogger formatting and SEO
   * @param {string} content - The HTML content
   * @param {string} metaDescription - Meta description for SEO
   * @returns {string} Enhanced HTML content
   */
  enhanceContentForBlogger(content, metaDescription) {
    let enhancedContent = content;

    // Add meta description as a hidden comment for SEO (some themes can use this)
    if (metaDescription) {
      enhancedContent = `<!-- META_DESCRIPTION: ${metaDescription} -->\n${enhancedContent}`;
    }

    // Ensure proper heading hierarchy (H1 should be the post title, start content with H2)
    enhancedContent = enhancedContent.replace(/<h1([^>]*)>/gi, '<h2$1>');
    enhancedContent = enhancedContent.replace(/<\/h1>/gi, '</h2>');

    // Enhance images with proper alt text and responsive classes
    enhancedContent = enhancedContent.replace(
      /<img([^>]*?)>/gi,
      '<img$1 style="max-width: 100%; height: auto;" loading="lazy">'
    );

    // Add proper paragraph spacing for better readability
    enhancedContent = enhancedContent.replace(/<p>/gi, '<p style="margin-bottom: 1em;">');

    // Enhance links to open in new tab for external links
    enhancedContent = enhancedContent.replace(
      /<a\s+href="(https?:\/\/[^"]*)"([^>]*)>/gi,
      '<a href="$1"$2 target="_blank" rel="noopener noreferrer">'
    );

    // Add structured data markup for better SEO
    const structuredData = `
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BlogPosting",
      "headline": "Article Content",
      "description": "${metaDescription || 'Blog post content'}",
      "datePublished": "${new Date().toISOString()}",
      "author": {
        "@type": "Person",
        "name": "Blog Author"
      }
    }
    </script>`;

    // Add the structured data at the end
    enhancedContent += structuredData;

    return enhancedContent;
  }

  async processImagesInContent(content) {
    // Find all local image references in the content
    const imageRegex = /<img[^>]+src="\/uploads\/([^"]+)"[^>]*>/g;
    const images = [];
    let match;

    while ((match = imageRegex.exec(content)) !== null) {
      images.push({
        fullMatch: match[0],
        filename: match[1],
        index: match.index
      });
    }

    console.log(`Found ${images.length} local images to process`);

    if (images.length === 0) {
      return content; // No images to process
    }

    // Process each image for public hosting
    let processedContent = content;
    const imageService = require('./imageService');

    for (const image of images) {
      try {
        // Create image data object
        const imageData = {
          filename: image.filename,
          path: require('path').join(__dirname, '../uploads', image.filename),
          url: `/uploads/${image.filename}`
        };

        // Process image for publishing (get public URL)
        const processedImage = await imageService.processImageForPublishing(imageData);

        // Replace local URL with public URL
        const localUrl = `/uploads/${image.filename}`;
        processedContent = processedContent.replace(localUrl, processedImage.publicUrl);

        console.log('Image URL replaced:', {
          filename: image.filename,
          from: localUrl,
          to: processedImage.publicUrl
        });
      } catch (error) {
        console.error(`Failed to process image ${image.filename}:`, error);
        // Keep original URL as fallback
        const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
        const localUrl = `/uploads/${image.filename}`;
        const absoluteUrl = `${baseUrl}/uploads/${image.filename}`;
        processedContent = processedContent.replace(localUrl, absoluteUrl);
      }
    }

    console.log('Images processed for publishing:', {
      imageCount: images.length,
      processedSuccessfully: true
    });

    return processedContent;
  }

  // Note: Blogger API v3 doesn't have direct image upload endpoints
  // Images need to be hosted externally or uploaded through the Blogger web interface
  async uploadImage(blogId, imageData) {
    console.warn('Direct image upload to Blogger is not supported by API v3');
    console.log('Image data received:', {
      mimeType: imageData.mimeType,
      filename: imageData.filename,
      dataLength: imageData.data?.length || 0
    });

    // In production, implement upload to your preferred image hosting service
    throw new Error('Direct image upload to Blogger is not supported. Please use external image hosting.');
  }
}

module.exports = new BloggerService();
