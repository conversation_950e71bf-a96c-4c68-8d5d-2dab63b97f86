const express = require('express');
const router = express.Router();
const config = require('../config/config');
const { validateApiKey, sanitizeInput } = require('../middleware/validation');

// Apply security middleware
router.use(sanitizeInput);

// Get current configuration (without sensitive data)
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: {
      rateLimitWindowMs: config.rateLimitWindowMs,
      rateLimitMaxRequests: config.rateLimitMaxRequests,
      scrapingTimeout: config.scrapingTimeout,
      maxContentLength: config.maxContentLength,
      nodeEnv: config.nodeEnv,
      hasGeminiKey: !!config.geminiApiKey,
      hasGoogleAuth: !!(config.google.clientId && config.google.clientSecret)
    }
  });
});

// Update Gemini API key (basic implementation)
router.post('/gemini-key', validateApi<PERSON>ey, (req, res) => {
  const { apiKey } = req.body;
  
  if (!apiKey) {
    return res.status(400).json({
      success: false,
      error: 'API key is required'
    });
  }

  // In a real application, you would want to:
  // 1. Validate the API key
  // 2. Store it securely (encrypted)
  // 3. Update the running configuration
  
  // For now, we'll just validate the format
  if (!apiKey.startsWith('AIza')) {
    return res.status(400).json({
      success: false,
      error: 'Invalid Gemini API key format'
    });
  }

  // Update the configuration (this is temporary and will reset on restart)
  config.geminiApiKey = apiKey;
  
  res.json({
    success: true,
    message: 'Gemini API key updated successfully'
  });
});

module.exports = router;
