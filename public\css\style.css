/* Custom Styles for Article Rewriter App */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Form Elements */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Progress Bar */
.progress {
    height: 0.75rem;
    border-radius: 0.375rem;
}

.progress-bar {
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

/* Content Areas */
.content-preview {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: #fff;
}

.content-preview h1,
.content-preview h2,
.content-preview h3 {
    color: var(--dark-color);
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.content-preview h1 {
    font-size: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.content-preview h2 {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.content-preview h3 {
    font-size: 1.1rem;
    color: var(--secondary-color);
}

.content-preview p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* Editor Styles */
.editor-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #fff;
}

.editor-toolbar {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.5rem;
    border-radius: 0.375rem 0.375rem 0 0;
}

.editor-content {
    min-height: 300px;
    padding: 1rem;
    outline: none;
}

/* Modal Styles */
.modal-header {
    background-color: var(--primary-color);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Loading Spinner */
.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Content Comparison */
.comparison-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .comparison-container {
        grid-template-columns: 1fr;
    }
}

/* Tag Styles */
.tag {
    display: inline-block;
    background-color: var(--light-color);
    color: var(--dark-color);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    margin: 0.125rem;
    border: 1px solid #dee2e6;
}

.tag.selected {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 576px) {
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

/* Settings Modal */
.settings-section {
    margin-bottom: 2rem;
}

.settings-section h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Publish Modal */
.blog-option {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.blog-option:hover {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
}

.blog-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

.blog-option .blog-name {
    font-weight: 600;
    color: var(--dark-color);
}

.blog-option .blog-url {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

/* Content Editor */
.content-editor {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    min-height: 400px;
}

.content-editor:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Telegram Sharing Styles */
.telegram-share-alert {
    border-left: 4px solid #0088cc;
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
}

.telegram-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.telegram-message {
    background: white;
    border-radius: 12px;
    padding: 12px 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e8ed;
    font-size: 14px;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
}

.telegram-message strong {
    color: #1a1a1a;
}

.telegram-message a {
    color: #0088cc;
    text-decoration: none;
}

.telegram-message a:hover {
    text-decoration: underline;
}

#telegramMessage {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
}

.fab.fa-telegram-plane {
    color: #0088cc;
}

/* Telegram responsive styles */
@media (max-width: 768px) {
    .telegram-share-alert .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .telegram-share-alert .btn {
        margin-top: 10px;
    }
}

/* Image Upload Styles */
.content-editor-toolbar {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 8px;
}

.content-editor-toolbar .btn {
    margin-right: 5px;
}

#imagePreviewContainer {
    margin-top: 15px;
}

#imagePreview {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-options-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

/* Image upload form styles */
#imageUploadForm .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Uploaded image styles in content */
.content-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 10px 0;
}

.content-preview img[style*="float: left"] {
    margin-right: 15px;
    margin-bottom: 10px;
}

.content-preview img[style*="float: right"] {
    margin-left: 15px;
    margin-bottom: 10px;
}

/* Image upload responsive styles */
@media (max-width: 768px) {
    .image-options-grid {
        grid-template-columns: 1fr;
    }

    .content-editor-toolbar {
        text-align: center;
    }

    .content-editor-toolbar .btn {
        margin: 2px;
    }
}
