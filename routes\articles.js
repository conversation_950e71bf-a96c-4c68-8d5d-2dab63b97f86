const express = require('express');
const router = express.Router();
const articleService = require('../services/articleService');
const scrapingService = require('../services/scrapingService');
const geminiService = require('../services/geminiService');
const bloggerService = require('../services/bloggerService');
const telegramService = require('../services/telegramService');
const adsenseService = require('../services/adsenseService');
const {
  validateUrl,
  validateScrapeRequest,
  validateRewriteRequest,
  validatePublishRequest,
  sanitizeInput,
  limitContentLength,
  limitUserRequests
} = require('../middleware/validation');

// Helper function to insert CTA button into content
function insertCtaButtonIntoContent(content, ctaConfig) {
  const { buttonText, targetUrl, buttonColor } = ctaConfig;

  // Generate unique ID for this CTA button
  const ctaId = 'cta-' + Date.now();

  // Create the CTA button HTML with countdown functionality
  const ctaButtonHtml = `
    <div class="cta-section" style="margin: 30px 0; padding: 25px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); border-radius: 10px; text-align: center; border: 1px solid #e0e6ed;">
      <h3 style="color: #2c3e50; margin-bottom: 15px; font-size: 1.4em;">Ready to Take Action?</h3>
      <p style="color: #5a6c7d; margin-bottom: 20px; font-size: 1.1em;">Don't miss out on this opportunity!</p>
      <button id="${ctaId}" class="cta-button" data-target-url="${targetUrl}" data-cta-id="${ctaId}"
              style="background-color: var(--bs-${buttonColor}); color: white; border: none; padding: 15px 30px; font-size: 1.2em; font-weight: bold; border-radius: 50px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0,0,0,0.2); text-decoration: none; display: inline-block;">
        ${buttonText}
      </button>
    </div>

    <!-- CTA Countdown Modal -->
    <div id="ctaModal-${ctaId}" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 10000; justify-content: center; align-items: center;">
      <div style="background: white; padding: 30px; border-radius: 15px; text-align: center; max-width: 500px; margin: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">You're being redirected...</h3>
        <div style="font-size: 3em; color: #e74c3c; margin: 20px 0; font-weight: bold;" id="countdown-${ctaId}">10</div>
        <p style="color: #5a6c7d; margin-bottom: 20px;">Please wait while we prepare your destination</p>

        <!-- AdSense Ad -->
        <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center;">
          <p style="color: #6c757d; margin: 0 0 10px 0; font-size: 12px; font-style: italic;">Advertisement</p>
          <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXX" crossorigin="anonymous"></script>
          <ins class="adsbygoogle"
               style="display:block; text-align:center;"
               data-ad-layout="in-article"
               data-ad-format="fluid"
               data-ad-client="ca-pub-XXXXXXXXXX"
               data-ad-slot="5678901234"></ins>
          <script>
               (adsbygoogle = window.adsbygoogle || []).push({});
          </script>
        </div>

        <button class="skip-cta-btn" data-target-url="${targetUrl}" data-cta-id="${ctaId}"
                style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 10px;">
          Skip (Continue Now)
        </button>
      </div>
    </div>

    <script>
      // CTA Button functionality with proper event listeners
      document.addEventListener('DOMContentLoaded', function() {
        // Handle CTA button clicks
        document.addEventListener('click', function(e) {
          if (e.target.classList.contains('cta-button')) {
            e.preventDefault();
            const targetUrl = e.target.dataset.targetUrl;
            const ctaId = e.target.dataset.ctaId;
            showCtaCountdown(targetUrl, ctaId);
          }

          // Handle skip button clicks
          if (e.target.classList.contains('skip-cta-btn')) {
            e.preventDefault();
            const targetUrl = e.target.dataset.targetUrl;
            const ctaId = e.target.dataset.ctaId;
            skipCtaCountdown(targetUrl, ctaId);
          }
        });

        // Add hover effects for CTA buttons
        document.addEventListener('mouseover', function(e) {
          if (e.target.classList.contains('cta-button')) {
            e.target.style.transform = 'translateY(-2px)';
            e.target.style.boxShadow = '0 6px 20px rgba(0,0,0,0.3)';
          }
        });

        document.addEventListener('mouseout', function(e) {
          if (e.target.classList.contains('cta-button')) {
            e.target.style.transform = 'translateY(0)';
            e.target.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
          }
        });
      });

      function showCtaCountdown(targetUrl, ctaId) {
        const modal = document.getElementById('ctaModal-' + ctaId);
        const countdownElement = document.getElementById('countdown-' + ctaId);

        modal.style.display = 'flex';

        let timeLeft = 10;
        const timer = setInterval(() => {
          timeLeft--;
          countdownElement.textContent = timeLeft;

          if (timeLeft <= 0) {
            clearInterval(timer);
            window.open(targetUrl, '_blank');
            modal.style.display = 'none';
          }
        }, 1000);

        // Store timer ID for potential cleanup
        modal.dataset.timerId = timer;
      }

      function skipCtaCountdown(targetUrl, ctaId) {
        const modal = document.getElementById('ctaModal-' + ctaId);
        const timerId = modal.dataset.timerId;

        if (timerId) {
          clearInterval(parseInt(timerId));
        }

        window.open(targetUrl, '_blank');
        modal.style.display = 'none';
      }

      // Close modal when clicking outside
      document.addEventListener('click', function(e) {
        if (e.target.id && e.target.id.startsWith('ctaModal-')) {
          const modal = e.target;
          const timerId = modal.dataset.timerId;

          if (timerId) {
            clearInterval(parseInt(timerId));
          }

          modal.style.display = 'none';
        }
      });
    </script>
  `;

  // Simply append CTA button at the end of content
  return content + ctaButtonHtml;
}

// Middleware to check authentication
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }
  next();
};

// Apply security middleware to all routes
router.use(sanitizeInput);
router.use(limitUserRequests(20, 60000)); // 20 requests per minute per user

// Validate URL
router.post('/validate-url', validateUrl, async (req, res, next) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'URL is required'
      });
    }

    const validation = await articleService.validateUrl(url);

    res.json({
      success: true,
      valid: validation.valid,
      error: validation.error,
      details: validation
    });
  } catch (error) {
    next(error);
  }
});

// Scrape article content from URL
router.post('/scrape', validateScrapeRequest, limitContentLength(), async (req, res, next) => {
  try {
    const { url, articleType } = req.body;
    
    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'URL is required'
      });
    }

    const scrapedContent = await scrapingService.scrapeArticle(url, articleType);
    
    res.json({
      success: true,
      data: scrapedContent
    });
  } catch (error) {
    next(error);
  }
});

// Rewrite article content using Gemini AI
router.post('/rewrite', validateRewriteRequest, limitContentLength(), async (req, res, next) => {
  try {
    const { content, title, articleType, customPrompt } = req.body;
    
    if (!content) {
      return res.status(400).json({
        success: false,
        error: 'Content is required'
      });
    }

    const rewrittenContent = await geminiService.rewriteContent({
      content,
      title,
      articleType,
      customPrompt
    });
    
    res.json({
      success: true,
      data: rewrittenContent
    });
  } catch (error) {
    console.error('Rewrite endpoint error:', error);

    // Provide detailed error information for debugging and user feedback
    let statusCode = 500;
    let userMessage = 'An unexpected error occurred while rewriting content';
    let technicalMessage = error.message;

    // Handle specific error types
    if (error.message.includes('API key')) {
      statusCode = 401;
      userMessage = 'Invalid or missing Gemini API key. Please check your configuration.';
    } else if (error.message.includes('quota') || error.message.includes('rate limit')) {
      statusCode = 429;
      userMessage = 'API quota exceeded. Please try again later.';
    } else if (error.message.includes('safety')) {
      statusCode = 400;
      userMessage = 'Content was flagged by safety filters. Please try different content.';
    } else if (error.message.includes('not found') || error.message.includes('404')) {
      statusCode = 503;
      userMessage = 'AI service temporarily unavailable. Please try again later.';
      technicalMessage = 'Gemini model not found - may need to update model name';
    } else if (error.message.includes('network') || error.message.includes('timeout')) {
      statusCode = 503;
      userMessage = 'Network error. Please check your connection and try again.';
    }

    res.status(statusCode).json({
      success: false,
      error: userMessage,
      technicalError: process.env.NODE_ENV === 'development' ? technicalMessage : undefined,
      timestamp: new Date().toISOString()
    });
  }
});

// Humanize article content using Gemini AI
router.post('/humanize', validateRewriteRequest, limitContentLength(), async (req, res, next) => {
  try {
    const { content, title, language } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        error: 'Content is required for humanization'
      });
    }

    console.log('Humanization request received:', {
      contentLength: content.length,
      title: title || 'No title',
      language: language || 'auto-detect'
    });

    const humanizedContent = await geminiService.humanizeContent({
      content,
      title,
      language
    });

    console.log('Humanization completed:', {
      success: humanizedContent.success,
      originalWordCount: humanizedContent.originalWordCount,
      humanizedWordCount: humanizedContent.humanizedWordCount
    });

    res.json({
      success: true,
      data: humanizedContent
    });
  } catch (error) {
    console.error('Humanization endpoint error:', error);

    // Provide detailed error information for debugging and user feedback
    let statusCode = 500;
    let userMessage = 'An unexpected error occurred while humanizing content';
    let technicalMessage = error.message;

    // Handle specific error types
    if (error.message.includes('API key')) {
      statusCode = 401;
      userMessage = 'Invalid or missing Gemini API key. Please check your configuration.';
    } else if (error.message.includes('quota') || error.message.includes('rate limit')) {
      statusCode = 429;
      userMessage = 'API quota exceeded. Please try again later.';
    } else if (error.message.includes('safety')) {
      statusCode = 400;
      userMessage = 'Content was flagged by safety filters. Please try different content.';
    } else if (error.message.includes('not found') || error.message.includes('404')) {
      statusCode = 503;
      userMessage = 'AI service temporarily unavailable. Please try again later.';
      technicalMessage = 'Gemini model not found - may need to update model name';
    } else if (error.message.includes('network') || error.message.includes('timeout')) {
      statusCode = 503;
      userMessage = 'Network error. Please check your connection and try again.';
    }

    res.status(statusCode).json({
      success: false,
      error: userMessage,
      technicalError: process.env.NODE_ENV === 'development' ? technicalMessage : undefined,
      timestamp: new Date().toISOString()
    });
  }
});

// Get user's Blogger blogs
router.get('/blogs', requireAuth, async (req, res, next) => {
  try {
    const blogs = await bloggerService.getUserBlogs(req.user);
    
    res.json({
      success: true,
      data: blogs
    });
  } catch (error) {
    next(error);
  }
});

// Publish article to Blogger
router.post('/publish', requireAuth, validatePublishRequest, limitContentLength(100000), async (req, res, next) => {
  try {
    console.log('Publishing request received:', {
      userId: req.user?.id,
      userEmail: req.user?.emails?.[0]?.value,
      hasAccessToken: !!req.user?.accessToken,
      hasRefreshToken: !!req.user?.refreshToken,
      bodyKeys: Object.keys(req.body)
    });

    const {
      blogId,
      title,
      content,
      labels,
      customUrl,
      metaDescription,
      isDraft = false,
      insertAdsenseAds = true, // Always true by default
      insertCtaButton = false,
      ctaConfig = null
    } = req.body;

    if (!blogId || !title || !content) {
      console.log('Publishing validation failed:', { blogId: !!blogId, title: !!title, content: !!content });
      return res.status(400).json({
        success: false,
        error: 'Blog ID, title, and content are required'
      });
    }

    console.log('Attempting to publish post:', {
      blogId,
      titleLength: title.length,
      contentLength: content.length,
      labelsCount: labels?.length || 0,
      hasCustomUrl: !!customUrl,
      hasMetaDescription: !!metaDescription,
      isDraft
    });

    // Process images in content before publishing
    let processedContent = await bloggerService.processImagesInContent(content);

    // Always insert AdSense ads for published articles (not drafts)
    if (!isDraft) {
      console.log('Processing content for automatic AdSense ad insertion...');

      // Get user ID for personalized settings
      const userId = req.user?.emails?.[0]?.value || req.user?.id;

      const adInsertionResult = adsenseService.insertAdsIntoContent(processedContent, {
        userId,
        autoInsertAds: true // Force ads to be inserted
      });

      if (adInsertionResult.success && adInsertionResult.adsInserted > 0) {
        processedContent = adInsertionResult.content;
        console.log('AdSense ads inserted automatically:', {
          adsInserted: adInsertionResult.adsInserted,
          totalParagraphs: adInsertionResult.totalParagraphs,
          userId: userId ? userId.substring(0, 10) + '...' : 'anonymous'
        });
      } else {
        console.log('AdSense ad insertion failed:', adInsertionResult.message);
      }
    } else {
      console.log('AdSense ad insertion skipped: draft post');
    }

    // Insert CTA button if enabled and not a draft
    if (!isDraft && insertCtaButton && ctaConfig) {
      console.log('Processing content for CTA button insertion...');
      processedContent = insertCtaButtonIntoContent(processedContent, ctaConfig);
      console.log('CTA button inserted successfully');
    } else {
      console.log('CTA button insertion skipped:', {
        isDraft,
        insertCtaButton,
        hasCtaConfig: !!ctaConfig,
        reason: isDraft ? 'draft post' : (!insertCtaButton ? 'user disabled CTA' : 'missing CTA config')
      });
    }

    const publishedPost = await bloggerService.publishPost(req.user, {
      blogId,
      title,
      content: processedContent,
      labels,
      customUrl,
      metaDescription,
      isDraft
    });

    console.log('Publishing successful:', {
      postId: publishedPost.id,
      postUrl: publishedPost.url,
      status: publishedPost.status
    });

    res.json({
      success: true,
      data: publishedPost
    });
  } catch (error) {
    console.error('Publishing error details:', {
      message: error.message,
      code: error.code,
      status: error.status,
      stack: error.stack,
      userId: req.user?.id,
      userTokens: {
        hasAccessToken: !!req.user?.accessToken,
        hasRefreshToken: !!req.user?.refreshToken
      }
    });
    next(error);
  }
});

// Process complete pipeline: scrape -> rewrite -> publish
router.post('/process', requireAuth, async (req, res, next) => {
  try {
    const { url, articleType, blogId, customPrompt } = req.body;
    
    if (!url || !blogId) {
      return res.status(400).json({
        success: false,
        error: 'URL and blog ID are required'
      });
    }

    const result = await articleService.processArticle({
      url,
      articleType,
      blogId,
      customPrompt,
      user: req.user
    });
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
});

// Generate Telegram message preview
router.post('/telegram/preview', async (req, res) => {
  try {
    const { title, content, url, labels } = req.body;

    if (!title || !content || !url) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: title, content, and url are required'
      });
    }

    console.log('Generating Telegram preview:', {
      titleLength: title.length,
      contentLength: content.length,
      hasUrl: !!url,
      labelsCount: labels?.length || 0
    });

    // Generate the message using TelegramService
    const message = telegramService.generateMessage({
      title,
      content,
      url,
      labels: labels || []
    });

    res.json({
      success: true,
      data: {
        message,
        isJobRelated: telegramService.isJobRelated(title, content),
        messageLength: message.length,
        chatId: telegramService.chatId,
        groupName: 'Pune private companies jobs 👍'
      }
    });

  } catch (error) {
    console.error('Telegram preview error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate Telegram preview'
    });
  }
});

// Send message to Telegram
router.post('/telegram/send', async (req, res) => {
  try {
    const { message, options = {} } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        error: 'Message is required'
      });
    }

    console.log('Sending custom message to Telegram:', {
      messageLength: message.length,
      hasOptions: Object.keys(options).length > 0
    });

    const result = await telegramService.sendMessage(message, options);

    if (result.success) {
      res.json({
        success: true,
        data: {
          messageId: result.messageId,
          message: 'Message sent to Telegram successfully'
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || 'Failed to send message to Telegram'
      });
    }

  } catch (error) {
    console.error('Telegram send error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to send message to Telegram'
    });
  }
});

// Share article to Telegram (auto-generate and send)
router.post('/telegram/share', async (req, res) => {
  try {
    const { title, content, url, labels, options = {} } = req.body;

    if (!title || !content || !url) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: title, content, and url are required'
      });
    }

    console.log('Sharing article to Telegram:', {
      titleLength: title.length,
      contentLength: content.length,
      hasUrl: !!url,
      labelsCount: labels?.length || 0
    });

    const result = await telegramService.shareArticle({
      title,
      content,
      url,
      labels: labels || []
    }, options);

    if (result.success) {
      res.json({
        success: true,
        data: {
          messageId: result.messageId,
          message: result.message,
          generatedMessage: result.generatedMessage
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || result.message
      });
    }

  } catch (error) {
    console.error('Telegram share error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to share article to Telegram'
    });
  }
});

// Test Telegram bot connection
router.get('/telegram/test', async (req, res) => {
  try {
    const result = await telegramService.testConnection();

    if (result.success) {
      res.json({
        success: true,
        data: {
          botInfo: result.botInfo,
          message: 'Telegram bot connection successful'
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        message: 'Failed to connect to Telegram bot'
      });
    }

  } catch (error) {
    console.error('Telegram test error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to test Telegram connection'
    });
  }
});

// AdSense Settings and Management Routes

// Get AdSense settings
router.get('/adsense/settings', async (req, res) => {
  try {
    // Get user ID for personalized settings
    const userId = req.user?.emails?.[0]?.value || req.user?.id;

    const settings = adsenseService.getSettings(userId);
    const adUnits = adsenseService.getAdUnits();

    res.json({
      success: true,
      data: {
        settings,
        adUnits,
        userId: userId ? userId.substring(0, 10) + '...' : 'anonymous'
      }
    });
  } catch (error) {
    console.error('Get AdSense settings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve AdSense settings'
    });
  }
});

// Update AdSense settings
router.post('/adsense/settings', requireAuth, async (req, res) => {
  try {
    const userId = req.user?.emails?.[0]?.value || req.user?.id;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User authentication required to save settings'
      });
    }

    const { settings } = req.body;

    if (!settings) {
      return res.status(400).json({
        success: false,
        error: 'Settings data is required'
      });
    }

    const updatedSettings = adsenseService.updateSettings(userId, settings);

    res.json({
      success: true,
      data: {
        settings: updatedSettings,
        message: 'Settings saved successfully'
      }
    });
  } catch (error) {
    console.error('Error updating AdSense settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update AdSense settings'
    });
  }
});

// Generate article preview with all features
router.post('/preview', requireAuth, async (req, res) => {
  try {
    const {
      content,
      title,
      insertAdsenseAds = false,
      insertCtaButton = false,
      ctaConfig = null
    } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        error: 'Content is required for preview'
      });
    }

    let previewContent = content;
    const previewInfo = {
      originalContentLength: content.length,
      features: [],
      adsInserted: 0,
      ctaButtonAdded: false,
      imagesProcessed: 0
    };

    // Get user ID for personalized settings
    const userId = req.user?.emails?.[0]?.value || req.user?.id;

    // Process images (check both local and ImgBB URLs)
    const localImageRegex = /<img[^>]+src="\/uploads\/([^"]+)"[^>]*>/g;
    const imgbbImageRegex = /<img[^>]+src="https:\/\/i\.ibb\.co\/[^"]+"/g;

    const localImageMatches = [...previewContent.matchAll(localImageRegex)];
    const imgbbImageMatches = [...previewContent.matchAll(imgbbImageRegex)];

    previewInfo.imagesProcessed = localImageMatches.length + imgbbImageMatches.length;

    if (localImageMatches.length > 0) {
      previewInfo.features.push(`${localImageMatches.length} image(s) will be uploaded to ImgBB`);
    }
    if (imgbbImageMatches.length > 0) {
      previewInfo.features.push(`${imgbbImageMatches.length} image(s) already hosted on ImgBB`);
    }

    // Insert AdSense ads if enabled
    if (insertAdsenseAds) {
      console.log('Generating AdSense ad preview...');
      const adInsertionResult = adsenseService.insertAdsIntoContent(previewContent, { userId });

      if (adInsertionResult.success && adInsertionResult.adsInserted > 0) {
        previewContent = adInsertionResult.content;
        previewInfo.adsInserted = adInsertionResult.adsInserted;
        previewInfo.features.push(`${adInsertionResult.adsInserted} AdSense ad(s) inserted`);
      } else {
        const message = adInsertionResult.message || 'Ad insertion failed';
        previewInfo.features.push('AdSense ads: ' + message);
      }
    }

    // Insert CTA button if enabled
    if (insertCtaButton && ctaConfig) {
      console.log('Generating CTA button preview...');
      previewContent = insertCtaButtonIntoContent(previewContent, ctaConfig);
      previewInfo.ctaButtonAdded = true;
      previewInfo.features.push(`CTA button added: "${ctaConfig.buttonText}"`);
    }

    // Add preview styling and indicators
    previewContent = addPreviewStyling(previewContent);

    res.json({
      success: true,
      data: {
        previewContent,
        previewInfo,
        title: title || 'Article Preview',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error generating article preview:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate article preview'
    });
  }
});

/**
 * Add preview styling and indicators to content
 * @param {string} content - HTML content
 * @returns {string} - Content with preview styling
 */
function addPreviewStyling(content) {
  // Add preview indicators for AdSense ads
  content = content.replace(
    /<div class="adsense-ad-container"[^>]*>/g,
    '<div class="adsense-ad-container" style="border: 2px dashed #4CAF50; background: #f0f8f0; position: relative;">' +
    '<div style="position: absolute; top: 5px; right: 5px; background: #4CAF50; color: white; padding: 2px 8px; font-size: 12px; border-radius: 3px;">📢 AdSense Ad</div>'
  );

  // Add preview indicators for CTA buttons
  content = content.replace(
    /<div class="cta-section"[^>]*>/g,
    '<div class="cta-section" style="border: 2px dashed #2196F3; background: #f0f4ff; position: relative; margin: 30px 0; padding: 25px;">' +
    '<div style="position: absolute; top: 5px; right: 5px; background: #2196F3; color: white; padding: 2px 8px; font-size: 12px; border-radius: 3px;">🎯 CTA Button</div>'
  );

  // Add preview indicators for local images (will be uploaded)
  content = content.replace(
    /<img([^>]+src="\/uploads\/[^"]+")([^>]*)>/g,
    '<div style="border: 2px dashed #FF9800; background: #fff8f0; position: relative; display: inline-block; margin: 5px;">' +
    '<div style="position: absolute; top: 5px; right: 5px; background: #FF9800; color: white; padding: 2px 8px; font-size: 12px; border-radius: 3px; z-index: 10;">📷 Will Upload</div>' +
    '<img$1$2 style="opacity: 0.8;">' +
    '</div>'
  );

  // Add preview indicators for ImgBB images (already uploaded)
  content = content.replace(
    /<img([^>]+src="https:\/\/i\.ibb\.co\/[^"]+")([^>]*)>/g,
    '<div style="border: 2px solid #4CAF50; background: #f0fff0; position: relative; display: inline-block; margin: 5px;">' +
    '<div style="position: absolute; top: 5px; right: 5px; background: #4CAF50; color: white; padding: 2px 8px; font-size: 12px; border-radius: 3px; z-index: 10;">✅ Hosted</div>' +
    '<img$1$2 style="opacity: 0.9;">' +
    '</div>'
  );

  return content;
}

// Update AdSense settings
router.post('/adsense/settings', async (req, res) => {
  try {
    const { settings, adUnits } = req.body;

    if (settings) {
      adsenseService.updateSettings(settings);
    }

    if (adUnits) {
      adsenseService.updateAdUnits(adUnits);
    }

    res.json({
      success: true,
      message: 'AdSense settings updated successfully',
      data: {
        settings: adsenseService.getSettings(),
        adUnits: adsenseService.getAdUnits()
      }
    });
  } catch (error) {
    console.error('Update AdSense settings error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update AdSense settings'
    });
  }
});

// Test ad insertion on content
router.post('/adsense/test', async (req, res) => {
  try {
    const { content, settings } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        error: 'Content is required for testing ad insertion'
      });
    }

    const result = adsenseService.insertAdsIntoContent(content, settings);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Test ad insertion error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test ad insertion'
    });
  }
});

// Process content with ads for publishing
router.post('/adsense/process', async (req, res) => {
  try {
    const { content, options } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        error: 'Content is required for ad processing'
      });
    }

    console.log('Processing content for ad insertion:', {
      contentLength: content.length,
      options: options || 'default'
    });

    const result = adsenseService.insertAdsIntoContent(content, options);

    console.log('Ad insertion result:', {
      success: result.success,
      adsInserted: result.adsInserted,
      message: result.message
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Process content with ads error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process content with ads'
    });
  }
});

// Insert AdSense ads into content manually (for content editor)
router.post('/insert-ads', requireAuth, async (req, res) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        error: 'Content is required'
      });
    }

    console.log('Manual AdSense ad insertion requested');

    // Get user ID for personalized settings
    const userId = req.user?.emails?.[0]?.value || req.user?.id;

    const adInsertionResult = adsenseService.insertAdsIntoContent(content, {
      userId,
      autoInsertAds: true // Force ads to be inserted
    });

    if (adInsertionResult.success) {
      res.json({
        success: true,
        content: adInsertionResult.content,
        adsInserted: adInsertionResult.adsInserted,
        message: adInsertionResult.message
      });
    } else {
      res.status(400).json({
        success: false,
        error: adInsertionResult.message || 'Failed to insert ads'
      });
    }

  } catch (error) {
    console.error('Error inserting ads:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error while inserting ads'
    });
  }
});

module.exports = router;
