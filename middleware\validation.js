const { body, validationResult } = require('express-validator');

// Validation middleware
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// URL validation rules
const validateUrl = [
  body('url')
    .isURL({ protocols: ['http', 'https'] })
    .withMessage('Must be a valid HTTP or HTTPS URL')
    .isLength({ max: 2048 })
    .withMessage('URL too long'),
  validateRequest
];

// Article scraping validation
const validateScrapeRequest = [
  body('url')
    .isURL({ protocols: ['http', 'https'] })
    .withMessage('Must be a valid HTTP or HTTPS URL'),
  body('articleType')
    .optional()
    .isIn(['general', 'news', 'blog', 'job', 'product', 'review', 'tutorial', 'research', 'press', 'interview'])
    .withMessage('Invalid article type'),
  validateRequest
];

// Content rewriting validation
const validateRewriteRequest = [
  body('content')
    .notEmpty()
    .withMessage('Content is required')
    .isLength({ max: 50000 })
    .withMessage('Content too long'),
  body('title')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Title too long'),
  body('articleType')
    .optional()
    .isIn(['general', 'news', 'blog', 'job', 'product', 'review', 'tutorial', 'research', 'press', 'interview'])
    .withMessage('Invalid article type'),
  body('customPrompt')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Custom prompt too long'),
  validateRequest
];

// Publishing validation
const validatePublishRequest = [
  body('blogId')
    .notEmpty()
    .withMessage('Blog ID is required')
    .isLength({ max: 50 })
    .withMessage('Invalid blog ID'),
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ max: 200 })
    .withMessage('Title too long'),
  body('content')
    .notEmpty()
    .withMessage('Content is required')
    .isLength({ max: 100000 })
    .withMessage('Content too long'),
  body('labels')
    .optional()
    .isArray()
    .withMessage('Labels must be an array'),
  body('labels.*')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Label too long'),
  body('customUrl')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Custom URL too long'),
  body('isDraft')
    .optional()
    .isBoolean()
    .withMessage('isDraft must be boolean'),
  validateRequest
];

// API key validation
const validateApiKey = [
  body('apiKey')
    .notEmpty()
    .withMessage('API key is required')
    .matches(/^AIza[0-9A-Za-z_-]{35}$/)
    .withMessage('Invalid Gemini API key format'),
  validateRequest
];

// Sanitization middleware
const sanitizeInput = (req, res, next) => {
  // Remove potentially dangerous HTML tags and scripts
  const sanitizeString = (str) => {
    if (typeof str !== 'string') return str;
    return str
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
      .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  };

  // Recursively sanitize object properties
  const sanitizeObject = (obj) => {
    if (typeof obj === 'string') {
      return sanitizeString(obj);
    } else if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    } else if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const key in obj) {
        sanitized[key] = sanitizeObject(obj[key]);
      }
      return sanitized;
    }
    return obj;
  };

  req.body = sanitizeObject(req.body);
  next();
};

// Content length limiter
const limitContentLength = (maxLength = 50000) => {
  return (req, res, next) => {
    if (req.body.content && req.body.content.length > maxLength) {
      return res.status(413).json({
        success: false,
        error: `Content too long. Maximum ${maxLength} characters allowed.`
      });
    }
    next();
  };
};

// Request frequency limiter per user
const userRateLimiter = new Map();

const limitUserRequests = (maxRequests = 10, windowMs = 60000) => {
  return (req, res, next) => {
    const userId = req.user?.id || req.ip;
    const now = Date.now();
    
    if (!userRateLimiter.has(userId)) {
      userRateLimiter.set(userId, { count: 1, resetTime: now + windowMs });
      return next();
    }
    
    const userLimit = userRateLimiter.get(userId);
    
    if (now > userLimit.resetTime) {
      userLimit.count = 1;
      userLimit.resetTime = now + windowMs;
      return next();
    }
    
    if (userLimit.count >= maxRequests) {
      return res.status(429).json({
        success: false,
        error: 'Too many requests. Please wait before trying again.',
        retryAfter: Math.ceil((userLimit.resetTime - now) / 1000)
      });
    }
    
    userLimit.count++;
    next();
  };
};

module.exports = {
  validateUrl,
  validateScrapeRequest,
  validateRewriteRequest,
  validatePublishRequest,
  validateApiKey,
  sanitizeInput,
  limitContentLength,
  limitUserRequests
};
