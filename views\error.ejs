<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                    <h1 class="mt-3"><%= error.status || 500 %></h1>
                    <h4 class="text-muted"><%= message %></h4>
                    
                    <% if (error.status === 404) { %>
                        <p class="mt-3">The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.</p>
                    <% } else { %>
                        <p class="mt-3">Something went wrong on our end. Please try again later.</p>
                    <% } %>
                    
                    <div class="mt-4">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Go Home
                        </a>
                        <button onclick="history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Go Back
                        </button>
                    </div>
                </div>
                
                <% if (error && error.stack && process.env.NODE_ENV === 'development') { %>
                    <div class="mt-5">
                        <h5>Error Details (Development Mode)</h5>
                        <pre class="bg-light p-3 rounded"><%= error.stack %></pre>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</body>
</html>
