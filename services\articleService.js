const scrapingService = require('./scrapingService');
const geminiService = require('./geminiService');
const bloggerService = require('./bloggerService');

class ArticleService {
  async processArticle({ url, articleType, blogId, customPrompt, user }) {
    try {
      console.log(`Starting article processing for URL: ${url}`);
      
      // Step 1: Scrape the article
      console.log('Step 1: Scraping article content...');
      const scrapedData = await scrapingService.scrapeArticle(url, articleType);
      
      if (!scrapedData.success) {
        throw new Error('Failed to scrape article content');
      }
      
      // Step 2: Rewrite content using Gemini AI
      console.log('Step 2: Rewriting content with Gemini AI...');
      const rewrittenData = await geminiService.rewriteContent({
        content: scrapedData.content,
        title: scrapedData.title,
        articleType: articleType,
        customPrompt: customPrompt
      });
      
      if (!rewrittenData.success) {
        throw new Error('Failed to rewrite article content');
      }
      
      // Step 3: Publish to Blogger (if user and blogId provided)
      let publishedData = null;
      if (user && blogId) {
        console.log('Step 3: Publishing to Blogger...');
        publishedData = await bloggerService.publishPost(user, {
          blogId: blogId,
          title: rewrittenData.title,
          content: rewrittenData.content,
          labels: rewrittenData.tags,
          customUrl: rewrittenData.permalink,
          isDraft: false
        });
      }
      
      // Combine all results
      const result = {
        success: true,
        pipeline: {
          scraping: {
            success: true,
            url: url,
            originalTitle: scrapedData.title,
            originalWordCount: scrapedData.wordCount,
            scrapedAt: scrapedData.scrapedAt
          },
          rewriting: {
            success: true,
            newTitle: rewrittenData.title,
            newWordCount: rewrittenData.rewrittenWordCount,
            rewrittenAt: rewrittenData.rewrittenAt
          },
          publishing: publishedData ? {
            success: true,
            postId: publishedData.id,
            postUrl: publishedData.url,
            publishedAt: publishedData.published
          } : null
        },
        originalData: scrapedData,
        rewrittenData: rewrittenData,
        publishedData: publishedData,
        processedAt: new Date().toISOString()
      };
      
      console.log('Article processing completed successfully');
      return result;
      
    } catch (error) {
      console.error('Article processing error:', error);
      throw error;
    }
  }

  async scrapeOnly(url, articleType) {
    try {
      const scrapedData = await scrapingService.scrapeArticle(url, articleType);
      return {
        success: true,
        data: scrapedData,
        processedAt: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async rewriteOnly({ content, title, articleType, customPrompt }) {
    try {
      const rewrittenData = await geminiService.rewriteContent({
        content,
        title,
        articleType,
        customPrompt
      });
      
      return {
        success: true,
        data: rewrittenData,
        processedAt: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async publishOnly(user, { blogId, title, content, labels, customUrl, isDraft }) {
    try {
      const publishedData = await bloggerService.publishPost(user, {
        blogId,
        title,
        content,
        labels,
        customUrl,
        isDraft
      });
      
      return {
        success: true,
        data: publishedData,
        processedAt: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async getSEOSuggestions(content, title) {
    try {
      const suggestions = await geminiService.generateSEOSuggestions(content, title);
      return {
        success: true,
        data: suggestions,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async validateUrl(url) {
    try {
      // Basic URL validation
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return {
          valid: false,
          error: 'URL must use HTTP or HTTPS protocol'
        };
      }
      
      // Try to access the URL with a HEAD request
      const axios = require('axios');
      const response = await axios.head(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; ArticleRewriter/1.0)'
        }
      });
      
      return {
        valid: true,
        status: response.status,
        contentType: response.headers['content-type'],
        contentLength: response.headers['content-length']
      };
      
    } catch (error) {
      return {
        valid: false,
        error: error.message,
        code: error.code
      };
    }
  }

  getArticleTypes() {
    return [
      { value: 'general', label: 'General Article' },
      { value: 'news', label: 'News Article' },
      { value: 'blog', label: 'Blog Post' },
      { value: 'job', label: 'Job Posting' },
      { value: 'product', label: 'Product Description' },
      { value: 'review', label: 'Review Article' },
      { value: 'tutorial', label: 'Tutorial/How-to' },
      { value: 'research', label: 'Research Article' },
      { value: 'press', label: 'Press Release' },
      { value: 'interview', label: 'Interview' }
    ];
  }

  async getProcessingStatus(processId) {
    // In a real application, you would store processing status in a database
    // For now, this is a placeholder for future implementation
    return {
      processId: processId,
      status: 'completed',
      message: 'Processing status tracking not implemented yet'
    };
  }

  generateProcessingReport(result) {
    if (!result.success) {
      return {
        success: false,
        error: 'Processing failed'
      };
    }
    
    const report = {
      summary: {
        originalUrl: result.pipeline.scraping.url,
        originalTitle: result.pipeline.scraping.originalTitle,
        newTitle: result.pipeline.rewriting.newTitle,
        originalWordCount: result.pipeline.scraping.originalWordCount,
        newWordCount: result.pipeline.rewriting.newWordCount,
        published: !!result.pipeline.publishing
      },
      timeline: [
        {
          step: 'Scraping',
          status: 'completed',
          timestamp: result.pipeline.scraping.scrapedAt
        },
        {
          step: 'Rewriting',
          status: 'completed',
          timestamp: result.pipeline.rewriting.rewrittenAt
        }
      ]
    };
    
    if (result.pipeline.publishing) {
      report.timeline.push({
        step: 'Publishing',
        status: 'completed',
        timestamp: result.pipeline.publishing.publishedAt,
        url: result.pipeline.publishing.postUrl
      });
    }
    
    return report;
  }
}

module.exports = new ArticleService();
