#!/bin/bash

# Test script to debug publishing endpoint
# Make sure you're logged in first at http://localhost:3000

echo "🔧 Testing publishing endpoint..."
echo "Make sure you're logged in at http://localhost:3000 first!"
echo ""

# Test with sample data
curl -X POST http://localhost:3000/api/articles/publish \
  -H "Content-Type: application/json" \
  -H "Cookie: connect.sid=YOUR_SESSION_COOKIE_HERE" \
  -d '{
    "blogId": "test-blog-id",
    "title": "Test Article - Debug Publishing",
    "content": "<p>This is a test article for debugging.</p><p>Testing the publishing functionality.</p>",
    "labels": ["test", "debug"],
    "metaDescription": "Test article for debugging publishing",
    "isDraft": true
  }' \
  -v

echo ""
echo "Check the server console for detailed error logs!"
