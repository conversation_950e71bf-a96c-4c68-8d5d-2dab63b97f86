/**
 * Image upload and management routes
 */

const express = require('express');
const multer = require('multer');
const router = express.Router();
const imageService = require('../services/imageService');

// Configure multer middleware
const upload = imageService.getMulterConfig();

/**
 * POST /api/images/upload
 * Upload a single image file
 */
router.post('/upload', upload.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                error: 'No image file provided'
            });
        }

        const altText = req.body.altText || '';
        const imageData = await imageService.processUploadedImage(req.file, altText);

        res.json({
            success: true,
            data: imageData,
            message: 'Image uploaded successfully'
        });

    } catch (error) {
        console.error('Image upload error:', error);
        
        let errorMessage = 'Failed to upload image';
        if (error.code === 'LIMIT_FILE_SIZE') {
            errorMessage = 'File size too large. Maximum size is 5MB.';
        } else if (error.message.includes('Invalid file type')) {
            errorMessage = error.message;
        }

        res.status(400).json({
            success: false,
            error: errorMessage
        });
    }
});

/**
 * DELETE /api/images/:filename
 * Delete an uploaded image
 */
router.delete('/:filename', async (req, res) => {
    try {
        const { filename } = req.params;
        
        if (!filename) {
            return res.status(400).json({
                success: false,
                error: 'Filename is required'
            });
        }

        const deleted = await imageService.deleteImage(filename);
        
        if (deleted) {
            res.json({
                success: true,
                message: 'Image deleted successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                error: 'Image not found or could not be deleted'
            });
        }

    } catch (error) {
        console.error('Image deletion error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to delete image'
        });
    }
});

/**
 * POST /api/images/generate-html
 * Generate HTML for image insertion
 */
router.post('/generate-html', async (req, res) => {
    try {
        const { imageData, options = {} } = req.body;

        if (!imageData) {
            return res.status(400).json({
                success: false,
                error: 'Image data is required'
            });
        }

        // Validate image data
        imageService.validateImageData(imageData);

        const html = imageService.generateImageHtml(imageData, options);

        res.json({
            success: true,
            data: { html },
            message: 'HTML generated successfully'
        });

    } catch (error) {
        console.error('HTML generation error:', error);
        res.status(400).json({
            success: false,
            error: error.message || 'Failed to generate HTML'
        });
    }
});

/**
 * GET /api/images/info/:filename
 * Get information about an uploaded image
 */
router.get('/info/:filename', async (req, res) => {
    try {
        const { filename } = req.params;
        
        if (!filename) {
            return res.status(400).json({
                success: false,
                error: 'Filename is required'
            });
        }

        const imageInfo = imageService.getImageInfo(filename);

        res.json({
            success: true,
            data: imageInfo
        });

    } catch (error) {
        console.error('Image info error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get image information'
        });
    }
});

// Error handling middleware for multer
router.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                error: 'File size too large. Maximum size is 5MB.'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                success: false,
                error: 'Too many files. Only one file allowed.'
            });
        }
    }
    
    res.status(500).json({
        success: false,
        error: error.message || 'Image processing error'
    });
});

module.exports = router;
