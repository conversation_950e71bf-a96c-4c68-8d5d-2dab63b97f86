class AdSenseSettingsManager {
    constructor() {
        this.settings = {};
        this.adUnits = [];
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.renderAdUnits();
    }

    setupEventListeners() {
        // Save settings button
        document.getElementById('saveSettingsBtn').addEventListener('click', () => {
            this.saveSettings();
        });

        // Add ad unit button
        document.getElementById('addAdUnitBtn').addEventListener('click', () => {
            this.addNewAdUnit();
        });

        // Test ad insertion button
        document.getElementById('testAdInsertionBtn').addEventListener('click', () => {
            this.toggleTestSection();
        });

        // Run test button
        document.getElementById('runTestBtn').addEventListener('click', () => {
            this.runAdInsertionTest();
        });

        // Settings change listeners
        document.getElementById('autoInsertAds').addEventListener('change', (e) => {
            this.settings.autoInsertAds = e.target.checked;
        });

        document.getElementById('adsPerArticle').addEventListener('change', (e) => {
            this.settings.adsPerArticle = parseInt(e.target.value);
        });

        document.getElementById('minParagraphsBetweenAds').addEventListener('change', (e) => {
            this.settings.minParagraphsBetweenAds = parseInt(e.target.value);
        });

        document.getElementById('skipFirstParagraphs').addEventListener('change', (e) => {
            this.settings.skipFirstParagraphs = parseInt(e.target.value);
        });

        document.getElementById('skipLastParagraphs').addEventListener('change', (e) => {
            this.settings.skipLastParagraphs = parseInt(e.target.value);
        });
    }

    async loadSettings() {
        try {
            const response = await fetch('/api/articles/adsense/settings');
            const result = await response.json();

            if (result.success) {
                this.settings = result.data.settings;
                this.adUnits = result.data.adUnits;
                this.populateSettingsForm();
            } else {
                this.showAlert('Failed to load settings', 'danger');
            }
        } catch (error) {
            console.error('Load settings error:', error);
            this.showAlert('Error loading settings', 'danger');
        }
    }

    populateSettingsForm() {
        document.getElementById('autoInsertAds').checked = this.settings.autoInsertAds;
        document.getElementById('adsPerArticle').value = this.settings.adsPerArticle;
        document.getElementById('minParagraphsBetweenAds').value = this.settings.minParagraphsBetweenAds;
        document.getElementById('skipFirstParagraphs').value = this.settings.skipFirstParagraphs;
        document.getElementById('skipLastParagraphs').value = this.settings.skipLastParagraphs;
    }

    renderAdUnits() {
        const container = document.getElementById('adUnitsContainer');
        container.innerHTML = '';

        this.adUnits.forEach((unit, index) => {
            const unitElement = this.createAdUnitElement(unit, index);
            container.appendChild(unitElement);
        });
    }

    createAdUnitElement(unit, index) {
        const template = document.getElementById('adUnitTemplate');
        const clone = template.content.cloneNode(true);
        const container = clone.querySelector('.ad-unit-card');

        container.dataset.unitId = unit.id;
        container.dataset.index = index;

        // Populate fields
        clone.querySelector('[data-field="name"]').value = unit.name;
        clone.querySelector('[data-field="id"]').textContent = unit.id;
        clone.querySelector('[data-field="enabled"]').checked = unit.enabled;
        clone.querySelector('[data-field="script"]').value = unit.script;

        // Add event listeners
        clone.querySelector('[data-field="name"]').addEventListener('change', (e) => {
            this.adUnits[index].name = e.target.value;
        });

        clone.querySelector('[data-field="enabled"]').addEventListener('change', (e) => {
            this.adUnits[index].enabled = e.target.checked;
        });

        clone.querySelector('[data-field="script"]').addEventListener('change', (e) => {
            this.adUnits[index].script = e.target.value;
        });

        clone.querySelector('.delete-ad-unit').addEventListener('click', () => {
            this.deleteAdUnit(index);
        });

        return clone;
    }

    addNewAdUnit() {
        const newUnit = {
            id: `ad-unit-${Date.now()}`,
            name: `Ad Unit ${this.adUnits.length + 1}`,
            script: `<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXX" crossorigin="anonymous"></script>
<ins class="adsbygoogle"
     style="display:block; text-align:center;"
     data-ad-layout="in-article"
     data-ad-format="fluid"
     data-ad-client="ca-pub-XXXXXXXXXX"
     data-ad-slot="XXXXXXXXXX"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>`,
            enabled: true
        };

        this.adUnits.push(newUnit);
        this.renderAdUnits();
        this.showAlert('New ad unit added. Don\'t forget to save your settings!', 'info');
    }

    deleteAdUnit(index) {
        if (confirm('Are you sure you want to delete this ad unit?')) {
            this.adUnits.splice(index, 1);
            this.renderAdUnits();
            this.showAlert('Ad unit deleted. Don\'t forget to save your settings!', 'warning');
        }
    }

    async saveSettings() {
        try {
            const saveBtn = document.getElementById('saveSettingsBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
            saveBtn.disabled = true;

            const response = await fetch('/api/articles/adsense/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    settings: this.settings,
                    adUnits: this.adUnits
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('Settings saved successfully!', 'success');
            } else {
                this.showAlert('Failed to save settings', 'danger');
            }
        } catch (error) {
            console.error('Save settings error:', error);
            this.showAlert('Error saving settings', 'danger');
        } finally {
            const saveBtn = document.getElementById('saveSettingsBtn');
            saveBtn.innerHTML = '<i class="fas fa-save me-1"></i>Save Settings';
            saveBtn.disabled = false;
        }
    }

    toggleTestSection() {
        const testSection = document.getElementById('testSection');
        const isVisible = testSection.style.display !== 'none';
        testSection.style.display = isVisible ? 'none' : 'block';

        const btn = document.getElementById('testAdInsertionBtn');
        btn.innerHTML = isVisible 
            ? '<i class="fas fa-vial me-1"></i>Test Ad Insertion'
            : '<i class="fas fa-times me-1"></i>Hide Test';
    }

    async runAdInsertionTest() {
        const testContent = document.getElementById('testContent').value.trim();
        
        if (!testContent) {
            this.showAlert('Please enter some test content', 'warning');
            return;
        }

        try {
            const runBtn = document.getElementById('runTestBtn');
            const originalText = runBtn.innerHTML;
            runBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Testing...';
            runBtn.disabled = true;

            const response = await fetch('/api/articles/adsense/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    content: testContent,
                    settings: this.settings
                })
            });

            const result = await response.json();

            if (result.success) {
                this.displayTestResults(result.data);
            } else {
                this.showAlert('Test failed: ' + result.error, 'danger');
            }
        } catch (error) {
            console.error('Test error:', error);
            this.showAlert('Error running test', 'danger');
        } finally {
            const runBtn = document.getElementById('runTestBtn');
            runBtn.innerHTML = '<i class="fas fa-play me-1"></i>Run Test';
            runBtn.disabled = false;
        }
    }

    displayTestResults(data) {
        const resultsContainer = document.getElementById('testResults');
        const outputContainer = document.getElementById('testOutput');

        let html = `
            <div class="row mb-3">
                <div class="col-md-3">
                    <strong>Ads Inserted:</strong> ${data.adsInserted}
                </div>
                <div class="col-md-3">
                    <strong>Total Paragraphs:</strong> ${data.totalParagraphs || 'N/A'}
                </div>
                <div class="col-md-6">
                    <strong>Status:</strong> ${data.message}
                </div>
            </div>
        `;

        if (data.adPositions && data.adPositions.length > 0) {
            html += `
                <div class="mb-3">
                    <strong>Ad Positions:</strong> ${data.adPositions.join(', ')}
                </div>
            `;
        }

        if (data.content) {
            html += `
                <div class="mb-3">
                    <strong>Preview (first 500 characters):</strong>
                    <div class="border p-2 bg-light" style="max-height: 200px; overflow-y: auto;">
                        <pre style="white-space: pre-wrap; font-size: 12px;">${data.content.substring(0, 500)}${data.content.length > 500 ? '...' : ''}</pre>
                    </div>
                </div>
            `;
        }

        outputContainer.innerHTML = html;
        resultsContainer.style.display = 'block';
    }

    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alertContainer');
        const alertId = 'alert-' + Date.now();
        
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.innerHTML = alertHtml;
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdSenseSettingsManager();
});
